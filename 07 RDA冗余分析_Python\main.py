# -*- coding: utf-8 -*-
"""
RDA冗余分析主程序
基于原R语言项目的主控制脚本，使用Python重新实现
"""

import sys
from pathlib import Path
import traceback

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent / "src"))

from config import *
from src.data_processing import DataProcessor
from src.rda_analysis_fixed import RDAAnalyzer
from src.visualization import RDAVisualizer
from src.export_results import ResultsExporter

def main_analysis():
    """主分析流程"""
    print("\n🚀 === 开始RDA冗余分析 ===")
    
    # 初始化各个模块
    processor = DataProcessor()
    analyzer = RDAAnalyzer()
    visualizer = RDAVisualizer()
    exporter = ResultsExporter()
    
    try:
        # 1. 配置验证和环境准备
        print("\n📋 === 配置验证和环境准备 ===")
        if not processor.validate_config():
            print("❌ 配置验证失败，程序退出")
            return None
            
        # 确保输出目录存在
        ensure_output_dirs()
        print(f"📁 输出目录已准备: {OUTPUT_DIR}")
        
        # 2. 数据加载和预处理
        print("\n📊 === 数据加载和预处理 ===")
        vif_data, change_data = processor.load_data()
        
        processed_result = processor.preprocess_data_with_transitions(
            vif_data, change_data, RESPONSE_VARS, create_transitions=True
        )
        
        # 显示转移模式摘要
        if processed_result['transition_patterns'] is not None:
            print("\n📈 转移模式摘要:")
            lu_matrix = processed_result['transition_patterns']['lu_matrix']
            st_matrix = processed_result['transition_patterns']['st_matrix']
            print(f"   土地利用转移类型数: {lu_matrix.shape[0]} × {lu_matrix.shape[1]}")
            print(f"   土壤类型转移类型数: {st_matrix.shape[0]} × {st_matrix.shape[1]}")
        
        # 3. 执行RDA分析
        if USE_STRATIFIED_RDA:
            print("\n📈 === 执行分层RDA分析 ===")
            rda_results = analyzer.perform_stratified_rda(
                processed_result, DEPTH_LAYERS, USE_COUNTY_COVARIATE
            )
            analysis_type = "stratified"
        else:
            print("\n📈 === 执行整体RDA分析 ===")
            rda_results = analyzer.perform_overall_rda(
                processed_result, USE_COUNTY_COVARIATE
            )
            analysis_type = "overall"
        
        # 4. 生成高质量图表
        print("\n🎨 === 生成图表 ===")
        visualizer.create_publication_plots(
            rda_results, processed_result, PLOTS_DIR, analysis_type
        )
        
        # 5. 导出详细结果
        print("\n💾 === 导出分析结果 ===")
        exporter.export_rda_results(
            rda_results, processed_result, REPORTS_DIR, analysis_type
        )
        
        # 创建摘要报告
        exporter.create_summary_report(
            rda_results, processed_result, OUTPUT_DIR, analysis_type
        )
        
        # 6. 分析完成
        print("\n📋 === 分析完成 ===")
        print("✅ RDA分析成功完成")
        
        if analysis_type == "overall" and rda_results is not None:
            print(f"✅ 解释度: {rda_results['explained_variance']:.2f}%")
            print(f"✅ 显著性: P = {rda_results['overall_pvalue']:.4f}")
        elif analysis_type == "stratified":
            valid_results = sum(1 for r in rda_results.values() if r is not None)
            print(f"✅ 有效分层结果: {valid_results}/{len(rda_results)}")
        
        print(f"\n🎉 === RDA分析完成 ===")
        print(f"📁 所有结果已保存至: {OUTPUT_DIR}")
        
        return rda_results
        
    except Exception as e:
        print(f"\n❌ === 分析过程中发生错误 ===")
        print(f"错误信息: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        return None

def display_config():
    """显示当前配置"""
    print("\n=== RDA冗余分析系统已加载 ===")
    print("📊 当前配置:")
    print(f"   - 分层RDA: {'启用' if USE_STRATIFIED_RDA else '禁用'}")
    print(f"   - 县域协变量: {'启用' if USE_COUNTY_COVARIATE else '禁用'}")
    print(f"   - 响应变量: {', '.join(RESPONSE_VARS)}")
    print(f"   - 输出目录: {OUTPUT_DIR}")
    print(f"   - 置换检验次数: {PERMUTATION_TESTS}")
    print(f"   - 缺失值阈值: {MISSING_THRESHOLD}")

if __name__ == "__main__":
    # 显示配置信息
    display_config()
    
    # 自动执行分析
    print("\n🚀 开始自动执行分析...")
    results = main_analysis()
    
    if results is not None:
        print("\n🎊 分析成功完成！")
        print("📊 可以查看以下输出文件:")
        print(f"   - 图表文件: {PLOTS_DIR}")
        print(f"   - 数据表格: {TABLES_DIR}")
        print(f"   - 分析报告: {REPORTS_DIR}")
    else:
        print("\n💥 分析失败，请检查错误信息并重试")