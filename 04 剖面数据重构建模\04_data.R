# =============================================================================
# 数据加载和预处理模块
# 
# 功能：
# 1. 包管理和配置加载
# 2. 数据文件加载（基础数据、完整数据）
# 3. 坐标投影转换
# 4. 相关性分析结果加载
# 5. 数据质量检查
# =============================================================================

# --- 通用工具函数 ---

# 创建输出目录
create_output_directories <- function(config) {
  base_dir <- config$data_paths$output_directory
  
  # 只创建主输出目录
  if(!dir.exists(base_dir)) {
    dir.create(base_dir, recursive = TRUE)
    cat("创建目录:", base_dir, "\n")
  }
}

# 空值合并操作符（R版本兼容性）
`%||%` <- function(x, y) {
  if(is.null(x)) y else x
}
# --- 加载必要的包 ---
load_required_packages <- function() {
  required_packages <- c(
    # 数据处理
    "readxl", "dplyr",

    # 空间数据
    "sp", "raster", "sf",

    # 地统计学
    "gstat",

    # 机器学习
    "randomForest",

    # 模型评估
    "yardstick",

    # 配置和工具
    "yaml", "openxlsx"
  )
  
  for(pkg in required_packages) {
    if(!require(pkg, character.only = TRUE)) {
      cat("安装包:", pkg, "\n")
      install.packages(pkg)
      library(pkg, character.only = TRUE)
    }
  }
  
  cat("所有必需包已加载完成\n")
}

# --- 加载配置文件 ---
load_config <- function(config_path = "02_config.yml") {
  if(!file.exists(config_path)) {
    stop("配置文件不存在: ", config_path)
  }
  
  config <- yaml::read_yaml(config_path)
  
  # 验证配置完整性
  required_sections <- c("data_paths", "counties", "years", "target_soil_properties")
  missing_sections <- setdiff(required_sections, names(config))

  if(length(missing_sections) > 0) {
    stop("配置文件缺少必需部分: ", paste(missing_sections, collapse = ", "))
  }

  # 验证数据路径配置
  required_paths <- c("basic_data_path", "full_data_path", "correlation_root", "output_directory")
  missing_paths <- setdiff(required_paths, names(config$data_paths))

  if(length(missing_paths) > 0) {
    stop("配置文件data_paths部分缺少必需路径: ", paste(missing_paths, collapse = ", "))
  }

  cat("配置文件加载成功\n")
  return(config)
}

# --- 坐标投影函数 ---
transform_coordinates <- function(data, config) {
  cat("正在进行坐标投影转换...\n")

  # 检查是否有坐标列
  lon_col <- config$column_names$longitude
  lat_col <- config$column_names$latitude

  if(!lon_col %in% names(data) || !lat_col %in% names(data)) {
    warning("缺少经纬度坐标列")
    return(data)
  }

  # 移除坐标缺失的行
  coord_complete <- complete.cases(data[, c(lon_col, lat_col)])
  if(sum(!coord_complete) > 0) {
    cat("移除", sum(!coord_complete), "个坐标缺失的样本\n")
    data <- data[coord_complete, ]
  }

  if(nrow(data) == 0) {
    warning("没有有效的坐标数据")
    return(data)
  }

  tryCatch({
    # 创建sf对象（WGS84地理坐标系）
    sf_data <- st_as_sf(data,
                        coords = c(lon_col, lat_col),
                        crs = 4326)  # WGS84

    # 投影到目标坐标系
    target_crs <- config$crs_target
    sf_projected <- st_transform(sf_data, crs = target_crs)

    # 提取投影后的坐标
    projected_coords <- st_coordinates(sf_projected)

    # 添加投影坐标到原数据
    data$proj_x <- projected_coords[, "X"]
    data$proj_y <- projected_coords[, "Y"]

    cat("坐标投影完成: WGS84 → EPSG:", target_crs, "\n")
    cat("投影坐标范围: X[", round(min(data$proj_x)), ",", round(max(data$proj_x)),
        "], Y[", round(min(data$proj_y)), ",", round(max(data$proj_y)), "]\n")

    return(data)

  }, error = function(e) {
    warning("坐标投影失败: ", e$message)
    cat("将使用原始经纬度坐标（不推荐用于克里金建模）\n")

    # 备用方案：直接使用经纬度
    data$proj_x <- data[[lon_col]]
    data$proj_y <- data[[lat_col]]

    return(data)
  })
}

# --- 完整数据加载函数（用于建模和预测）---
load_soil_data <- function(config) {
  cat("正在加载完整土壤数据（包含环境变量）...\n")

  # 读取完整数据文件（包含环境变量）
  if(!file.exists(config$data_paths$full_data_path)) {
    stop("完整数据文件不存在: ", config$data_paths$full_data_path)
  }

  data <- readxl::read_excel(config$data_paths$full_data_path)
  
  # 基本数据验证
  required_columns <- c(
    config$column_names$county,
    config$column_names$year,
    config$column_names$longitude,
    config$column_names$latitude,
    config$column_names$depth,
    config$column_names$profile_id
  )
  
  missing_columns <- setdiff(required_columns, names(data))
  if(length(missing_columns) > 0) {
    stop("数据缺少必需列: ", paste(missing_columns, collapse = ", "))
  }
  
  # 筛选目标县城和年份
  if(config$experiment$test_mode) {
    target_counties <- config$experiment$test_counties
    target_years <- config$experiment$test_years
    cat("测试模式：筛选", paste(target_counties, collapse=", "), "县", paste(target_years, collapse=", "), "年\n")
  } else {
    target_counties <- config$counties
    target_years <- config$years
    cat("完整模式：筛选", length(target_counties), "个县城", length(target_years), "个年份\n")
  }

  data <- data %>%
    filter(
      !!sym(config$column_names$county) %in% target_counties,
      !!sym(config$column_names$year) %in% target_years
    )

  cat("数据加载完成，共", nrow(data), "条记录\n")

  # 进行坐标投影转换
  data <- transform_coordinates(data, config)

  return(data)
}

# --- 相关性分析结果加载 ---
load_vif_results <- function(config, county, soil_property, year = NULL) {
  # 确定年份
  if(is.null(year)) {
    if(config$experiment$test_mode) {
      year <- config$experiment$test_years[1]
    } else {
      year <- config$years[1]  # 使用第一个年份作为默认值
    }
  }

  # 构建相关性分析结果文件路径
  correlation_file <- file.path(
    config$data_paths$correlation_root,
    county,
    as.character(year),
    "相关性分析报告.xlsx"
  )

  if(!file.exists(correlation_file)) {
    warning("相关性分析结果文件不存在: ", correlation_file)
    return(NULL)
  }

  # 读取指定土壤属性的相关性分析结果
  correlation_results <- tryCatch({
    readxl::read_excel(correlation_file, sheet = soil_property)
  }, error = function(e) {
    warning("读取相关性分析结果失败: ", e$message)
    return(NULL)
  })

  if(is.null(correlation_results) || nrow(correlation_results) == 0) {
    warning("相关性分析结果为空或读取失败")
    return(NULL)
  }

  cat("相关性分析结果加载成功，共", nrow(correlation_results), "个变量\n")

  # 验证必需列
  required_cols <- c("辅助变量", "相关系数 (r)", "P值")
  missing_cols <- setdiff(required_cols, names(correlation_results))

  if(length(missing_cols) > 0) {
    cat("缺少列:", paste(missing_cols, collapse = ", "), "\n")
    cat("可用列名:", paste(names(correlation_results), collapse = ", "), "\n")
    return(NULL)
  }

  # 筛选显著相关的变量
  significant_vars <- correlation_results[correlation_results$`P值` < 0.05, "辅助变量"][[1]]
  cat("显著相关变量(p<0.05):", length(significant_vars), "个\n")

  if(length(significant_vars) == 0) {
    cat("警告：没有发现显著相关的环境变量\n")
  }

  cat("相关性分析完成，将在建模时根据实际样本量进行变量选择\n")
  cat("可用于选择的变量总数:", nrow(correlation_results), "个\n")
  
  # 返回完整的相关性分析结果供后续使用
  return(list(
    correlation_data = correlation_results,
    significant_vars = significant_vars,
    all_vars_sorted = correlation_results[order(abs(correlation_results$`相关系数 (r)`), decreasing = TRUE), "辅助变量"][[1]]
  ))
}

# --- 基于建模样本量的协变量选择函数 ---
select_vars_by_n <- function(corr_data, n_samples, config) {
  
  # 获取样本量规则和阈值
  rules <- config$general_options$adaptive_variable_selection$sample_size_rules
  thresholds <- config$general_options$adaptive_variable_selection$significance_thresholds
  min_r <- config$general_options$adaptive_variable_selection$correlation_thresholds$min_abs_r
  
  # 确定最大变量数
  max_vars <- 1  # 默认值
  for(rule in rules) {
    if(n_samples >= rule$sample_range[1] && n_samples <= rule$sample_range[2]) {
      max_vars <- rule$max_variables
      break
    }
  }
  
  # 准备数据：计算绝对相关系数
  corr_data$abs_r <- abs(corr_data$`相关系数 (r)`)
  
  # 首先筛选满足相关性强度要求的变量
  corr_data <- corr_data[corr_data$abs_r >= min_r, ]
  
  if(nrow(corr_data) == 0) {
    cat("警告：无变量满足相关性强度阈值(|r|>=", min_r, ")\n")
    return(c())
  }
  
  # 分类变量：高显著 vs 次显著
  high_sig <- corr_data[corr_data$`P值` < thresholds$primary, ]  # p<0.05
  marginal_sig <- corr_data[corr_data$`P值` >= thresholds$primary & 
                            corr_data$`P值` < thresholds$marginal, ]  # 0.05≤p<0.1
  
  selected_vars <- c()
  
  # 步骤1：优先选择高显著变量（按相关性强度排序）
  if(nrow(high_sig) > 0) {
    high_sig_sorted <- high_sig[order(high_sig$abs_r, decreasing = TRUE), ]
    n_select <- min(nrow(high_sig_sorted), max_vars)
    selected_vars <- head(high_sig_sorted$辅助变量, n_select)
  }
  
  # 步骤2：如果高显著变量不足，补充次显著高相关变量
  if(length(selected_vars) < max_vars && nrow(marginal_sig) > 0) {
    remaining_slots <- max_vars - length(selected_vars)
    marginal_sorted <- marginal_sig[order(marginal_sig$abs_r, decreasing = TRUE), ]
    additional_vars <- head(marginal_sorted$辅助变量, remaining_slots)
    selected_vars <- c(selected_vars, additional_vars)
  }
  
  cat("样本量:", n_samples, "| 最大变量数:", max_vars, "| 高显著:", 
      sum(selected_vars %in% high_sig$辅助变量), "个 | 次显著:", 
      sum(selected_vars %in% marginal_sig$辅助变量), "个\n")
  
  return(unique(selected_vars))
}


# --- 分析缺失值函数 ---
analyze_missing_values_by_depth <- function(soil_data, county, year, soil_property, config) {

  cat("分析", soil_property, "的缺失值情况...\n")

  # 筛选特定县城和年份的数据
  subset_data <- soil_data %>%
    filter(
      !!sym(config$column_names$county) == county,
      !!sym(config$column_names$year) == year
    )

  if(nrow(subset_data) == 0) {
    return(list(total_missing = 0, layers_with_missing = list()))
  }

  # 分析每个深度层
  layers_with_missing <- list()
  total_missing <- 0

  for(depth_config in config$general_options$depth_layers) {
    layer_name <- depth_config$name
    center_depth <- depth_config$center

    # 筛选指定深度的数据
    layer_data <- subset_data %>%
      filter(!!sym(config$column_names$depth) == center_depth)

    if(nrow(layer_data) == 0) {
      cat("  ", layer_name, ": 无数据\n")
      next
    }

    # 统计缺失值
    if(soil_property %in% names(layer_data)) {
      missing_count <- sum(is.na(layer_data[[soil_property]]))
      total_count <- nrow(layer_data)
      cat("  ", layer_name, ": ", missing_count, "/", total_count, " 缺失\n")

      if(missing_count > 0) {
        layers_with_missing <- append(layers_with_missing, list(list(
          layer_name = layer_name,
          center_depth = center_depth,
          missing_count = missing_count,
          total_count = total_count
        )))
        total_missing <- total_missing + missing_count
      }
    }
  }

  return(list(
    total_missing = total_missing,
    layers_with_missing = layers_with_missing
  ))
}

# --- 支持多县城的缺失值分析函数 ---
analyze_missing_by_depth_multi <- function(soil_data, counties, year, soil_property, config) {

  cat("分析", soil_property, "的缺失值情况（", paste(counties, collapse=","), "）...\n")

  # 筛选多个县城和指定年份的数据
  # 确保年份类型匹配
  year_col <- soil_data[[config$column_names$year]]
  if(is.character(year_col) && is.numeric(year)) {
    year <- as.character(year)
  } else if(is.numeric(year_col) && is.character(year)) {
    year <- as.numeric(year)
  }

  subset_data <- soil_data %>%
    filter(
      !!sym(config$column_names$county) %in% counties,
      !!sym(config$column_names$year) == year
    )

  if(nrow(subset_data) == 0) {
    return(list(total_missing = 0, layers_with_missing = list()))
  }

  # 分析每个深度层
  layers_with_missing <- list()
  total_missing <- 0

  for(depth_config in config$general_options$depth_layers) {
    layer_name <- depth_config$name
    center_depth <- depth_config$center

    # 筛选指定深度的数据
    layer_data <- subset_data %>%
      filter(!!sym(config$column_names$depth) == center_depth)

    if(nrow(layer_data) == 0) {
      cat("  ", layer_name, ": 无数据\n")
      next
    }

    # 统计缺失值
    missing_count <- sum(is.na(layer_data[[soil_property]]))
    total_count <- nrow(layer_data)

    cat("  ", layer_name, ": ", missing_count, "/", total_count, " 缺失\n")

    if(missing_count > 0) {
      layers_with_missing[[layer_name]] <- list(
        missing_count = missing_count,
        total_count = total_count,
        center_depth = center_depth
      )
      total_missing <- total_missing + missing_count
    }
  }

  return(list(
    total_missing = total_missing,
    layers_with_missing = layers_with_missing
  ))
}
