# -*- coding: utf-8 -*-
"""
RDA冗余分析项目配置文件
基于原R语言项目的配置参数
"""

import os
from pathlib import Path

# =============================================================================
# 文件路径配置
# =============================================================================

# 项目根目录
PROJECT_ROOT = Path(__file__).parent

# 数据文件路径
VIF_FILE_PATH = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\嫩江_VIF_筛选变化量结果\分组VIF_筛选结果.xlsx"
CHANGE_FILE_PATH = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\嫩江_模型预测数据\嫩江_变化量.xlsx"

# 输出目录
OUTPUT_DIR = PROJECT_ROOT / "outputs"
PLOTS_DIR = OUTPUT_DIR / "plots"
TABLES_DIR = OUTPUT_DIR / "tables"
REPORTS_DIR = OUTPUT_DIR / "reports"

# =============================================================================
# 分析配置参数
# =============================================================================

# 分析配置开关
USE_COUNTY_COVARIATE = False  # 是否将县域作为协变量
COUNTY_COLUMN_NAME = "City"   # 县域列名
USE_STRATIFIED_RDA = False   # 是否进行分层RDA（改为整体分析以增加样本数）

# 缺失值处理配置
MISSING_THRESHOLD = 0.3       # 缺失值阈值（30%）
NUMERIC_MISSING_METHOD = "mean"
CATEGORICAL_MISSING_METHOD = "mode"

# RDA显著性检验参数
PERMUTATION_TESTS = 999

# 深度分层定义（基于实际深度中点值：5, 20, 45, 80）
DEPTH_LAYERS = {
    "0-10cm": [5],      # 深度中点5对应0-10cm
    "10-30cm": [20],    # 深度中点20对应10-30cm
    "30-60cm": [45],    # 深度中点45对应30-60cm
    "60-100cm": [80]    # 深度中点80对应60-100cm
}

# 响应变量定义
RESPONSE_VARS = ["△pH", "△SOM", "△TN", "△TP", "△物理粘粒"]

# =============================================================================
# 图表配置
# =============================================================================

PLOT_CONFIG = {
    'width': 12,
    'height': 10,
    'dpi': 300,
    'font_size': 14,
    'title_size': 16,
    'axis_size': 12,
    'legend_size': 11,
    'point_size': 2.5,
    'arrow_size': 1.2,
    'colors': ["#2E86AB", "#A23B72", "#F18F01", "#C73E1D", "#6A994E", "#577590"]
}

# RDA颜色方案
RDA_COLORS = {
    'primary': "#2E86AB",
    'secondary': "#A23B72",
    'accent': "#F18F01",
    'warning': "#C73E1D",
    'success': "#6A994E",
    'neutral': "#577590",
    'gradient': ["#2E86AB", "#4A90A4", "#689B9D", "#86A596", "#A4B08F", "#C2BA88"],
    'soil_vars': ["#8B4513", "#CD853F", "#DEB887", "#F4A460", "#D2691E"],
    'env_vars': ["#228B22", "#32CD32", "#90EE90", "#98FB98", "#00FF7F"]
}

# =============================================================================
# 变量分组定义
# =============================================================================

# 基础信息列
BASE_COLS = [
    "ProfileID", "Longitude", "Latitude", "City", "Location", 
    "Soilclass-1980", "Soilclass-2023", "LandUse-1980", "LandUse-2023",
    "深度范围", "深度中点"
]

# 变量分组（基于VIF分析脚本的变量分组）
VARIABLE_GROUPS = {
    'climate': [
        'Air_Temperature_2m_Mean', 'LST', 'Total_Precipitation_Mean', 'Total_Precipitation_Sum',
        'Total_Evaporation_Mean', 'Surface_Net_Solar_Radiation_Mean', 'Surface_Net_Thermal_Radiation_Mean',
        'Wind_U_Component_10m_Mean', 'Wind_V_Component_10m_Mean'
    ],
    'terrain': [
        '250DEM', 'Aspect', 'Slope', 'Plan_Curvature', 'Profile_Curvature',
        'Terrain_Ruggedness_Index', 'Topographic_Position_Index', 'Topographic_Wetness_Index',
        'Valley_Depth', 'Vertical_Distance_to_Channel_Network', 'Flow_Accumulation',
        'Flow_Direction', 'Sink_Route'
    ],
    'vegetation': [
        'NDVI', 'EVI', 'NDWI', 'MNDWI', 'SAVI', 'RVI', 'LAI', 'FAPAR', 'FVC',
        'NPP', 'GPP', 'BSI', 'IBI', 'Coloration_Index', 'Redness_Index', 'Saturation_Index'
    ],
    'human': [
        'Population', 'GDP', 'NTL', 'Built_Up', 'CLCD'
    ],
    'soil_3d': [
        'bdod', 'cec', 'Soil_Temperature_Mean', 'Soil_Water_Content_Mean'
    ],
    'categorical': [
        'landform', 'lithology', 'soiltype'
    ]
}

# 静态协变量：地形 + 分类变量
STATIC_VARS = VARIABLE_GROUPS['terrain'] + VARIABLE_GROUPS['categorical']

# 动态协变量：气候 + 植被 + 人类活动 + 3D土壤
DYNAMIC_VARS = (VARIABLE_GROUPS['climate'] + VARIABLE_GROUPS['vegetation'] + 
                VARIABLE_GROUPS['human'] + VARIABLE_GROUPS['soil_3d'])

# =============================================================================
# 中文显示名称映射
# =============================================================================

# 变量中文名称映射
VARIABLE_CHINESE_NAMES = {
    # 气候变量
    'Air_Temperature_2m_Mean': '气温',
    'LST': '地表温度',
    'Total_Precipitation_Mean': '降水量',
    'Total_Precipitation_Sum': '总降水',
    'Total_Evaporation_Mean': '蒸发量',
    'Surface_Net_Solar_Radiation_Mean': '净太阳辐射',
    'Surface_Net_Thermal_Radiation_Mean': '净热辐射',
    'Wind_U_Component_10m_Mean': '风速U分量',
    'Wind_V_Component_10m_Mean': '风速V分量',

    # 地形变量
    '250DEM': 'DEM高程',
    'Aspect': '坡向',
    'Slope': '坡度',
    'Plan_Curvature': '平面曲率',
    'Profile_Curvature': '剖面曲率',
    'Terrain_Ruggedness_Index': '地形粗糙度',
    'Topographic_Position_Index': '地形位置指数',
    'Topographic_Wetness_Index': '地形湿润指数',
    'Valley_Depth': '谷深',
    'Vertical_Distance_to_Channel_Network': '到河网距离',
    'Flow_Accumulation': '汇流累积量',
    'Flow_Direction': '流向',
    'Sink_Route': '汇流路径',

    # 植被变量
    'NDVI': '归一化植被指数',
    'EVI': '增强植被指数',
    'NDWI': '归一化水体指数',
    'MNDWI': '修正水体指数',
    'SAVI': '土壤调节植被指数',
    'RVI': '比值植被指数',
    'LAI': '叶面积指数',
    'FAPAR': '光合有效辐射',
    'FVC': '植被覆盖度',
    'NPP': '净初级生产力',
    'GPP': '总初级生产力',
    'BSI': '裸土指数',
    'IBI': '不透水面指数',
    'Coloration_Index': '色彩指数',
    'Redness_Index': '红度指数',
    'Saturation_Index': '饱和度指数',

    # 人类活动变量
    'Population': '人口密度',
    'GDP': '国内生产总值',
    'NTL': '夜间灯光',
    'Built_Up': '建设用地',
    'CLCD': '土地覆盖',

    # 3D土壤变量
    'bdod': '土壤容重',
    'cec': '阳离子交换量',
    'Soil_Temperature_Mean': '土壤温度',
    'Soil_Water_Content_Mean': '土壤含水量',

    # 分类变量
    'landform': '地貌类型',
    'lithology': '岩性类型',
    'soiltype': '土壤类型'
}

# 分组中文名称映射
GROUP_CHINESE_NAMES = {
    'climate': '气候',
    'terrain': '地形',
    'vegetation': '植被',
    'human': '人类活动',
    'soil_3d': '3D土壤',
    'categorical': '分类变量'
}

# =============================================================================
# 工具函数
# =============================================================================

def ensure_output_dirs():
    """确保输出目录存在"""
    for dir_path in [OUTPUT_DIR, PLOTS_DIR, TABLES_DIR, REPORTS_DIR]:
        dir_path.mkdir(parents=True, exist_ok=True)

def get_chinese_name(variable_name):
    """获取变量的中文名称"""
    return VARIABLE_CHINESE_NAMES.get(variable_name, variable_name)

def get_group_chinese_name(group_name):
    """获取分组的中文名称"""
    return GROUP_CHINESE_NAMES.get(group_name, group_name)