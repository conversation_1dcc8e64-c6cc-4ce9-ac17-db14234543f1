# =============================================================================
# 建模数据准备模块
#
# 功能：
# 1. 环境变量提取和处理
# 2. 缺失值填补数据准备
# 3. 邻近深度层变量处理
# 4. 统一建模数据准备
# =============================================================================

# --- 环境协变量提取函数 ---
extract_environmental_values <- function(layer_data, selected_vars, config, county, year, depth_layer_name, silent = FALSE) {
  if(is.null(selected_vars) || length(selected_vars) == 0) {
    return(layer_data)
  }

  # 检查选择的环境变量是否已经在原数据中
  existing_vars <- intersect(selected_vars, names(layer_data))
  missing_vars <- setdiff(selected_vars, names(layer_data))

  if(!silent) {
    cat("检查环境变量可用性:\n")
    cat("  - 原数据中存在:", length(existing_vars), "个:", paste(existing_vars, collapse = ", "), "\n")

    if(length(missing_vars) > 0) {
      cat("  - 警告：缺少环境变量:", length(missing_vars), "个:", paste(missing_vars, collapse = ", "), "\n")
      cat("  - 将只使用可用的环境变量\n")
    } else {
      cat("  - 所有环境变量都在原数据中\n")
    }
  }

  return(layer_data)
}

# --- 辅助函数：提取深度层数据 ---
extract_layer_data <- function(soil_data, county, year, layer_name, config) {
  # 获取深度层配置
  depth_layers <- config$general_options$depth_layers
  layer_names <- sapply(depth_layers, function(x) x$name)
  target_depth <- depth_layers[[which(layer_names == layer_name)[1]]]$center

  # 筛选数据
  layer_data <- soil_data[
    soil_data[[config$column_names$year]] == year &
    soil_data[[config$column_names$county]] == county &
    soil_data[[config$column_names$depth]] == target_depth,
  ]

  if(nrow(layer_data) == 0) {
    cat("无基础数据\n")
    return(NULL)
  }

  return(layer_data)
}

# --- 辅助函数：提取县城年份数据 ---
extract_county_year_data <- function(soil_data, county, year, config) {
  county_year_data <- soil_data[
    soil_data[[config$column_names$year]] == year &
    soil_data[[config$column_names$county]] == county,
  ]
  return(county_year_data)
}

# --- 辅助函数：提取多县城年份数据 ---
extract_multi_county_data <- function(soil_data, counties, year, config) {
  multi_county_data <- soil_data[
    soil_data[[config$column_names$year]] == year &
    soil_data[[config$column_names$county]] %in% counties,
  ]
  return(multi_county_data)
}

# --- 辅助函数：提取多县城深度层数据 ---
extract_multi_layer_data <- function(soil_data, counties, year, layer_name, config) {
  # 获取深度层配置
  depth_layers <- config$general_options$depth_layers
  layer_names <- sapply(depth_layers, function(x) x$name)
  target_depth <- depth_layers[[which(layer_names == layer_name)[1]]]$center

  # 筛选数据
  layer_data <- soil_data[
    soil_data[[config$column_names$year]] == year &
    soil_data[[config$column_names$county]] %in% counties &
    soil_data[[config$column_names$depth]] == target_depth,
  ]

  if(nrow(layer_data) == 0) {
    cat("无基础数据\n")
    return(NULL)
  }

  return(layer_data)
}

# --- 邻近深度层变量添加函数 ---
add_neighbor_layer_variables <- function(target_data, all_data, target_layer, config, current_soil_property) {
  depth_layers <- config$general_options$depth_layers
  # 只处理当前土壤属性（因为主程序是逐个属性处理的）
  soil_property <- current_soil_property

  # 直接添加所有邻近层变量，不做预筛选
  for(i in 1:nrow(target_data)) {
    current_point <- target_data[i, ]
    point_profile_id <- current_point[[config$column_names$profile_id]]
    point_year <- current_point[[config$column_names$year]]

    # 找到同一采样点的其他深度层数据
    same_point_data <- all_data %>%
      filter(
        !!sym(config$column_names$profile_id) == point_profile_id,
        !!sym(config$column_names$year) == point_year
      )

    # 为所有邻近层添加变量
    for(depth_config in depth_layers) {
      layer_name <- depth_config$name

      # 跳过目标深度层
      if(layer_name == target_layer) next

      depth_center <- depth_config$center

      # 获取该深度层的数据
      layer_data <- same_point_data %>%
        filter(!!sym(config$column_names$depth) == depth_center)

      neighbor_var_name <- paste0(soil_property, "_", gsub("-", "_", layer_name))

      # 直接添加值（包括NA），让后续统一处理
      if(nrow(layer_data) > 0) {
        target_data[i, neighbor_var_name] <- layer_data[[soil_property]][1]
      } else {
        target_data[i, neighbor_var_name] <- NA
      }
    }
  }

  # 统计添加的邻近层变量
  neighbor_pattern <- paste0("^", soil_property, "_[0-9]+_[0-9]+cm$")
  neighbor_vars <- names(target_data)[grepl(neighbor_pattern, names(target_data))]

  return(target_data)
}


# --- 数据标准化函数 ---
standardize_data <- function(data, target_var, feature_vars, method = "z_score") {
  # 数据标准化函数，支持Z-score标准化
  # 参数:
  #   data: 数据框
  #   target_var: 目标变量名
  #   feature_vars: 特征变量名向量
  #   method: 标准化方法，目前支持"z_score"
  # 返回:
  #   list(data = 标准化后的数据, scaler_info = 标准化参数信息)

  if(method != "z_score") {
    stop("目前只支持z_score标准化方法")
  }

  standardized_data <- data
  scaler_info <- list()

  # 标准化目标变量
  if(target_var %in% names(data)) {
    target_values <- data[[target_var]]
    target_mean <- mean(target_values, na.rm = TRUE)
    target_sd <- sd(target_values, na.rm = TRUE)

    if(target_sd > 0) {
      standardized_data[[target_var]] <- (target_values - target_mean) / target_sd
      scaler_info[[target_var]] <- list(mean = target_mean, sd = target_sd)
      cat("目标变量", target_var, "标准化: 均值 =", round(target_mean, 4), ", 标准差 =", round(target_sd, 4), "\n")
    } else {
      cat("警告：目标变量", target_var, "标准差为0，跳过标准化\n")
      scaler_info[[target_var]] <- list(mean = target_mean, sd = 1)
    }
  }

  # 标准化特征变量
  for(var in feature_vars) {
    if(var %in% names(data)) {
      var_values <- data[[var]]

      # 跳过因子变量（如县城变量）
      if(is.factor(var_values) || is.character(var_values)) {
        cat("跳过分类变量", var, "的标准化\n")
        scaler_info[[var]] <- list(mean = NA, sd = NA, type = "categorical")
        next
      }

      var_mean <- mean(var_values, na.rm = TRUE)
      var_sd <- sd(var_values, na.rm = TRUE)

      if(var_sd > 0) {
        standardized_data[[var]] <- (var_values - var_mean) / var_sd
        scaler_info[[var]] <- list(mean = var_mean, sd = var_sd)
      } else {
        cat("警告：特征变量", var, "标准差为0，跳过标准化\n")
        scaler_info[[var]] <- list(mean = var_mean, sd = 1)
      }
    }
  }

  cat("数据标准化完成，共标准化", length(scaler_info), "个变量\n")

  return(list(
    data = standardized_data,
    scaler_info = scaler_info
  ))
}

# --- 数据反标准化函数 ---
inverse_standardize <- function(values, scaler_info, var_name) {
  # 数据反标准化函数
  # 参数:
  #   values: 需要反标准化的数值向量
  #   scaler_info: 标准化参数信息
  #   var_name: 变量名
  # 返回:
  #   反标准化后的数值向量

  if(var_name %in% names(scaler_info)) {
    scaler <- scaler_info[[var_name]]

    # 检查是否为分类变量
    if(!is.null(scaler$type) && scaler$type == "categorical") {
      cat("分类变量", var_name, "无需反标准化\n")
      return(values)
    }

    mean_val <- scaler$mean
    sd_val <- scaler$sd
    return(values * sd_val + mean_val)
  } else {
    cat("警告：未找到变量", var_name, "的标准化信息，返回原值\n")
    return(values)
  }
}

# --- 数据准备函数（同时准备建模数据和预测数据）---
prepare_data <- function(layer_data, soil_property, config, method_type = "all", available_env_vars = NULL, include_prediction_data = FALSE, silent = FALSE) {

  if(!silent) cat("=== 建模数据准备（", method_type, "方法）===\n")

  # 第一步：确定核心变量和坐标系统
  # 确定坐标变量
  coord_vars <- if("proj_x" %in% names(layer_data) && "proj_y" %in% names(layer_data)) {
    if(!silent) cat("使用投影坐标: proj_x, proj_y\n")
    c("proj_x", "proj_y")
  } else {
    if(!silent) cat("使用原始坐标: x, y\n")
    c(config$column_names$longitude, config$column_names$latitude)
  }

  # 添加县城特征（如果是合并模式）
  county_vars <- c()
  if(config$combine_counties && config$column_names$county %in% names(layer_data)) {
    # 将县城转换为因子变量
    layer_data[[config$column_names$county]] <- as.factor(layer_data[[config$column_names$county]])
    county_vars <- config$column_names$county
    if(!silent) cat("添加县城特征变量:", config$column_names$county, "\n")
  }

  # 检查坐标完整性（所有样本都需要完整的坐标）
  core_vars <- c(coord_vars, county_vars)
  core_complete <- complete.cases(layer_data[, core_vars])
  valid_samples <- layer_data[core_complete, ]

  cat("坐标完整样本:", nrow(valid_samples), "个（从", nrow(layer_data), "个中筛选）\n")

  # 检查建模样本数量（只检查目标变量非缺失的样本）
  modeling_sample_count <- sum(!is.na(valid_samples[[soil_property]]))
  cat("其中可用于建模的样本:", modeling_sample_count, "个\n")

  if(modeling_sample_count < config$general_options$min_samples_for_modeling) {
    cat("可建模样本数不足，无法建模\n")
    return(NULL)
  }

  if(include_prediction_data) {
    prediction_sample_count <- sum(is.na(valid_samples[[soil_property]]))
    cat("需要预测的样本:", prediction_sample_count, "个\n")
  }

  # 第二步：处理环境变量
  final_env_vars <- c()
  if(method_type %in% c("regression_kriging", "random_forest", "all")) {

    if(!is.null(available_env_vars) && length(available_env_vars) > 0) {
      # 使用传入的环境变量列表，检查在所有有效样本中的可用性
      for(var in available_env_vars) {
        if(var %in% names(valid_samples)) {
          missing_count <- sum(is.na(valid_samples[[var]]))
          missing_rate <- missing_count / nrow(valid_samples)

          if(missing_rate <= config$general_options$max_variable_missing_rate) {  # 使用统一的缺失率阈值
            final_env_vars <- c(final_env_vars, var)

            if(missing_count > 0) {
              # 对缺失值进行均值填补
              mean_val <- mean(valid_samples[[var]], na.rm = TRUE)
              valid_samples[[var]][is.na(valid_samples[[var]])] <- mean_val
              cat("环境变量", var, ": 缺失", missing_count, "个（", round(missing_rate*100, 1), "%），用均值填补\n")
            } else {
              cat("环境变量", var, ": 完全可用\n")
            }
          } else {
            cat("环境变量", var, ": 缺失", missing_count, "个（", round(missing_rate*100, 1), "%），超过阈值", round(config$general_options$max_variable_missing_rate*100, 1), "%，排除\n")
          }
        } else {
          cat("环境变量", var, ": 在数据中不存在\n")
        }
      }

      cat("最终可用环境变量:", length(final_env_vars), "个\n")
    } else {
      cat("未提供环境变量或环境变量为空\n")
    }
  }

  # 第三步：处理邻近深度层变量
  # 只匹配当前处理属性的邻近层变量（如处理pH时只匹配 pH_0_10cm, pH_10_30cm等）
  neighbor_pattern <- paste0("^", soil_property, "_[0-9]+_[0-9]+cm$")
  neighbor_vars <- names(valid_samples)[grepl(neighbor_pattern, names(valid_samples))]
  available_neighbor_vars <- c()

  # 所有方法都处理邻近层变量，确保数据一致性
  if(length(neighbor_vars) > 0) {

    for(var in neighbor_vars) {
      missing_count <- sum(is.na(valid_samples[[var]]))
      missing_rate <- missing_count / nrow(valid_samples)

      if(missing_rate <= config$general_options$max_variable_missing_rate) {  # 使用统一的缺失率阈值
        available_neighbor_vars <- c(available_neighbor_vars, var)

        if(missing_count > 0) {
          # 对缺失值进行均值填补
          mean_val <- mean(valid_samples[[var]], na.rm = TRUE)
          valid_samples[[var]][is.na(valid_samples[[var]])] <- mean_val
          cat("邻近层变量", var, ": 缺失", missing_count, "个（", round(missing_rate*100, 1), "%），用均值填补\n")
        } else {
          cat("邻近层变量", var, ": 完全可用\n")
        }
      } else {
        cat("邻近层变量", var, ": 缺失率过高（", round(missing_rate*100, 1), "%），排除\n")
      }
    }

    cat("可用邻近层变量:", length(available_neighbor_vars), "个\n")
  }

  # 第四步：数据标准化
  # 构建特征列表（坐标 + 县城 + 环境变量 + 邻近层变量）
  all_feature_names <- c(coord_vars, county_vars, final_env_vars, available_neighbor_vars)

  # 对所有数据进行标准化
  cat("开始数据标准化...\n")
  standardization_result <- standardize_data(valid_samples, soil_property, all_feature_names)
  standardized_data <- standardization_result$data
  scaler_info <- standardization_result$scaler_info

  # 第五步：根据目标变量是否缺失分离建模数据和预测数据
  modeling_data <- standardized_data[!is.na(standardized_data[[soil_property]]), ]
  prediction_data <- if(include_prediction_data) {
    standardized_data[is.na(standardized_data[[soil_property]]), ]
  } else {
    NULL
  }

  cat("数据分离完成:\n")
  cat("  - 建模数据:", nrow(modeling_data), "个样本\n")
  if(include_prediction_data) {
    cat("  - 预测数据:", nrow(prediction_data), "个样本\n")
  }

  # 第六步：组装最终结果
  result <- list(
    modeling_data = modeling_data,
    prediction_data = prediction_data,
    sample_size = nrow(modeling_data),
    target_property = soil_property,
    core_vars = coord_vars,
    env_vars = final_env_vars,
    neighbor_vars = available_neighbor_vars,
    feature_names = all_feature_names,
    total_features = length(all_feature_names),
    scaler_info = scaler_info  # 标准化信息
  )

  cat("数据准备完成: 建模样本", result$sample_size, "个，特征", result$total_features, "个\n")
  cat("  - 坐标变量:", length(coord_vars), "个\n")
  cat("  - 环境变量:", length(result$env_vars), "个\n")
  cat("  - 邻近层变量:", length(result$neighbor_vars), "个\n")

  return(result)
}
