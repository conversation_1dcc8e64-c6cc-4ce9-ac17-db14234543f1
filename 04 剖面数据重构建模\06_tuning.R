# =============================================================================
# 参数调优模块（修改：对所有样本均进行调优）
#
# 功能：
# 1. 统一参数调优框架
# 2. 交叉验证策略确定
# 3. 各方法的网格搜索
# 4. 交叉验证评估
# =============================================================================

# --- 参数集合性能比较函数 ---
is_better_parameter_set <- function(cv_result, best_r2, best_cv_result, config) {
  # 检查输入有效性
  if(is.null(cv_result) || !is.finite(cv_result$r_squared)) {
    return(FALSE)
  }

  # 第一个有效结果
  if(is.null(best_cv_result)) {
    return(TRUE)
  }

  # 智能参数选择：优先比较R²，R²接近时比较RMSE
  r2_diff <- cv_result$r_squared - best_r2

  if(r2_diff > config$method_comparison$r2_tolerance) {
    # R²明显更好（差异 > 容忍度）
    return(TRUE)
  } else if(abs(r2_diff) <= config$method_comparison$r2_tolerance) {
    # R²接近（差异 ≤ 容忍度），比较RMSE（更小更好）
    return(cv_result$rmse < best_cv_result$rmse)
  } else {
    # R²明显更差
    return(FALSE)
  }
}

# --- 统一参数调优函数（修改：对所有样本均进行调优）---
tune_model_parameters <- function(data, target_var, feature_cols, config, method) {

  # 获取调优配置
  tuning_config <- config$auto_tuning
  n_samples <- nrow(data)

  cat("样本数", n_samples, "，开始参数调优\n")

  # 确定交叉验证策略
  cv_strategy <- determine_cv_strategy(n_samples, tuning_config)

  # 使用方法分发器（函数式编程优化）
  tuning_functions <- list(
    random_forest = function(...) tune_random_forest_grid(data, target_var, feature_cols, config, cv_strategy),
    regression_kriging = function(...) tune_regression_kriging_grid(data, target_var, feature_cols, config, cv_strategy),
    ordinary_kriging = function(...) tune_ordinary_kriging_grid(data, target_var, feature_cols, config, cv_strategy)
  )

  tuning_func <- tuning_functions[[method]]
  if(!is.null(tuning_func)) {
    return(tuning_func())
  } else {
    cat("警告：未知的建模方法:", method, "\n")
    return(NULL)
  }
}

# --- 确定交叉验证策略 ---
determine_cv_strategy <- function(n_samples, tuning_config) {
  if(tuning_config$cv_strategy == "adaptive") {
    adaptive_cv <- tuning_config$adaptive_cv
    if(n_samples <= adaptive_cv$loo_threshold) {
      list(type = "留一法", folds = n_samples)
    } else {
      list(type = "k折", folds = adaptive_cv$kfold_num)
    }
  } else if(tuning_config$cv_strategy == "loo") {
    list(type = "留一法", folds = n_samples)
  } else {
    kfold_num <- tuning_config$adaptive_cv$kfold_num %||% 5
    list(type = "k折", folds = kfold_num)
  }
}

# --- 随机森林网格搜索 ---
tune_random_forest_grid <- function(data, target_var, feature_cols, config, cv_strategy) {

  rf_config <- config$random_forest
  tune_params <- rf_config$tune

  # 创建参数网格
  param_grid <- expand.grid(
    ntree = tune_params$ntree,
    nodesize = tune_params$nodesize,
    mtry_factor = tune_params$mtry_factor,
    stringsAsFactors = FALSE
  )

  best_r2 <- -Inf
  best_params <- NULL
  best_cv_result <- NULL

  cat("随机森林参数调优: 测试", nrow(param_grid), "种参数组合\n")
  pb <- txtProgressBar(0, nrow(param_grid), style = 3)

  for(i in 1:nrow(param_grid)) {
    params <- param_grid[i, ]
    # 获取完整的评估结果
    cv_result <- perform_cv_evaluation(data, target_var, feature_cols, params, cv_strategy, "random_forest", return_full_metrics = TRUE)

    # 使用公共函数进行参数性能比较
    if(is_better_parameter_set(cv_result, best_r2, best_cv_result, config)) {
      best_r2 <- cv_result$r_squared
      best_params <- list(ntree = params$ntree, nodesize = params$nodesize, mtry_factor = params$mtry_factor)
      best_cv_result <- cv_result  # 保存完整的评估结果
    }
    setTxtProgressBar(pb, i)
  }
  close(pb)

  # 检查是否找到有效参数
  if(is.null(best_params)) {
    cat("随机森林调优失败：未找到有效参数组合\n")
    return(NULL)
  }

  cat("最佳参数: ntree =", best_params$ntree,
      ", nodesize =", best_params$nodesize,
      ", mtry_factor =", best_params$mtry_factor,
      ", CV R² =", round(best_r2, 3), "\n")
  cv_result <- best_cv_result

  # 使用统一函数合并评估指标
  best_params <- merge_evaluation_metrics(best_params, cv_result, best_r2)

  return(best_params)
}

# --- 回归克里金网格搜索 ---
tune_regression_kriging_grid <- function(data, target_var, feature_cols, config, cv_strategy) {

  rk_config <- config$regression_kriging
  tune_params <- rk_config$tune
  vgm_models <- rk_config$vgm_models

  # 创建参数网格
  param_grid <- expand.grid(
    nmax = tune_params$nmax,
    nmin = tune_params$nmin,
    vgm_model = vgm_models,
    stringsAsFactors = FALSE
  )

  best_r2 <- -Inf
  best_params <- NULL
  best_cv_result <- NULL

  cat("回归克里金参数调优: 测试", nrow(param_grid), "种参数组合\n")

  # 创建R内置进度条
  pb <- txtProgressBar(min = 0, max = nrow(param_grid), style = 3, width = 50, char = "=")

  for(i in 1:nrow(param_grid)) {
    params <- param_grid[i, ]

    # 获取完整的评估结果
    invisible(capture.output({
      cv_result <- suppressMessages(
        suppressWarnings(
          perform_cv_evaluation(data, target_var, feature_cols, params, cv_strategy, "regression_kriging", return_full_metrics = TRUE)
        )
      )
    }, type = "output"))

    # 使用公共函数进行参数性能比较
    if(is_better_parameter_set(cv_result, best_r2, best_cv_result, config)) {
      best_r2 <- cv_result$r_squared
      best_params <- list(
        nmax = params$nmax,
        nmin = params$nmin,
        vgm_model = params$vgm_model
      )
      best_cv_result <- cv_result  # 保存完整的评估结果
    }

    # 更新进度条
    setTxtProgressBar(pb, i)
  }

  close(pb)  # 关闭进度条

  # 检查是否找到有效参数
  if(is.null(best_params)) {
    cat("回归克里金调优失败：未找到有效参数组合\n")
    return(NULL)
  }

  # 直接使用调优阶段保存的完整评估结果
  cv_result <- best_cv_result

  # 使用统一函数合并评估指标
  best_params <- merge_evaluation_metrics(best_params, cv_result, best_r2)

  # 性能保护：如果调优后性能太差，给出警告
  if(best_params$r_squared < -0.5) {
    cat("警告：调优后性能较差 (R² =", round(best_params$r_squared, 3), ")，可能需要检查数据或方法适用性\n")
  }

  cat("最佳参数: nmax =", best_params$nmax,
      ", nmin =", best_params$nmin,
      ", vgm_model =", best_params$vgm_model,
      ", CV R² =", round(best_params$r_squared, 3), "\n")

  return(best_params)
}

# --- 普通克里金网格搜索 ---
tune_ordinary_kriging_grid <- function(data, target_var, feature_cols, config, cv_strategy) {

  ok_config <- config$ordinary_kriging
  tune_params <- ok_config$tune
  vgm_models <- ok_config$vgm_models

  # 创建参数网格（基于文献分析，专注于核心参数nmax, nmin, vgm_model）
  param_grid <- expand.grid(
    nmax = tune_params$nmax,
    nmin = tune_params$nmin,
    vgm_model = vgm_models,
    stringsAsFactors = FALSE
  )

  best_r2 <- -Inf
  best_params <- NULL
  best_cv_result <- NULL

  cat("普通克里金参数调优: 测试", nrow(param_grid), "种参数组合\n")

  # 创建R内置进度条
  pb <- txtProgressBar(min = 0, max = nrow(param_grid), style = 3, width = 50, char = "=")

  for(i in 1:nrow(param_grid)) {
    params <- param_grid[i, ]

    # 获取完整的评估结果
    invisible(capture.output({
      cv_result <- suppressMessages(
        suppressWarnings(
          perform_cv_evaluation(data, target_var, c(), params, cv_strategy, "ordinary_kriging", return_full_metrics = TRUE)
        )
      )
    }, type = "output"))

    # 使用公共函数进行参数性能比较
    if(is_better_parameter_set(cv_result, best_r2, best_cv_result, config)) {
      best_r2 <- cv_result$r_squared
      best_params <- list(
        nmax = params$nmax,
        nmin = params$nmin,
        vgm_model = params$vgm_model
      )
      best_cv_result <- cv_result  # 保存完整的评估结果
    }

    # 更新进度条
    setTxtProgressBar(pb, i)
  }

  close(pb)  # 关闭进度条

  # 检查是否找到有效参数
  if(is.null(best_params)) {
    cat("普通克里金调优失败：未找到有效参数组合\n")
    return(NULL)
  }

  # 直接使用调优阶段保存的完整评估结果（避免重复建模）
  cv_result <- best_cv_result

  # 使用统一函数合并评估指标
  best_params <- merge_evaluation_metrics(best_params, cv_result, best_r2)

  # 性能保护：如果调优后性能太差，给出警告
  if(best_params$r_squared < -0.5) {
    cat("警告：调优后性能较差 (R² =", round(best_params$r_squared, 3), ")，可能需要检查数据或方法适用性\n")
  }

  cat("最佳参数: nmax =", best_params$nmax,
      ", nmin =", best_params$nmin,
      ", vgm_model =", best_params$vgm_model,
      ", CV R² =", round(best_params$r_squared, 3), "\n")
  cat("使用固定maxdist_factor = 0.7（基于文献分析，非调优重点）\n")

  return(best_params)
}
