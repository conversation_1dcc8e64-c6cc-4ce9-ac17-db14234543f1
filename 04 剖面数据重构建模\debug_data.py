import pandas as pd
import numpy as np

# 加载数据
print("=== 调试数据筛选问题 ===")

# 尝试多个可能的数据文件路径
possible_paths = [
    r"E:\05 Python\Devway\01 硕士论文\00 原始数据\典型县\模型预测结果\东北_完整数据.xlsx",
    r"E:\05 Python\Devway\01 硕士论文\00 原始数据\标准化-两期数据合并.xlsx",
    r"E:\05 Python\Devway\01 硕士论文\00 原始数据\标准化-整理剖面数据完整版.xlsx"
]

data_path = None
for path in possible_paths:
    try:
        import os
        if os.path.exists(path):
            data_path = path
            print(f"找到数据文件: {data_path}")
            break
    except:
        continue

if data_path is None:
    print("未找到数据文件，尝试第一个路径...")
    data_path = possible_paths[0]

try:
    df = pd.read_excel(data_path)
    print(f"数据加载成功，总行数: {len(df)}")
    
    # 检查列名
    print(f"\n列名: {list(df.columns)}")
    
    # 检查年份列
    if 'year' in df.columns:
        print(f"\n年份列信息:")
        print(f"  数据类型: {df['year'].dtype}")
        print(f"  唯一值: {sorted(df['year'].unique())}")
        print(f"  各年份数量:")
        print(df['year'].value_counts().sort_index())
    else:
        print("\n错误：找不到'year'列！")
        print("可能的年份相关列:")
        year_cols = [col for col in df.columns if 'year' in col.lower() or '年' in col]
        print(year_cols)
    
    # 检查县城列
    if 'City' in df.columns:
        print(f"\n县城列信息:")
        print(f"  数据类型: {df['City'].dtype}")
        print(f"  唯一值: {sorted(df['City'].unique())}")
        print(f"  各县城数量:")
        print(df['City'].value_counts().sort_index())
    else:
        print("\n错误：找不到'City'列！")
        print("可能的县城相关列:")
        city_cols = [col for col in df.columns if 'city' in col.lower() or '县' in col or 'City' in col]
        print(city_cols)
    
    # 检查深度列
    if 'Depth' in df.columns:
        print(f"\n深度列信息:")
        print(f"  数据类型: {df['Depth'].dtype}")
        print(f"  唯一值: {sorted(df['Depth'].unique())}")
        print(f"  各深度数量:")
        print(df['Depth'].value_counts().sort_index())
    else:
        print("\n错误：找不到'Depth'列！")
        print("可能的深度相关列:")
        depth_cols = [col for col in df.columns if 'depth' in col.lower() or '深度' in col or 'Depth' in col]
        print(depth_cols)
    
    # 测试筛选逻辑
    print(f"\n=== 测试筛选逻辑 ===")
    
    # 测试1980年筛选
    if 'year' in df.columns:
        year_1980 = df[df['year'] == 1980]
        print(f"1980年数据行数: {len(year_1980)}")
        
        year_2023 = df[df['year'] == 2023]
        print(f"2023年数据行数: {len(year_2023)}")
        
        # 测试县城筛选
        if 'City' in df.columns:
            counties = ["嫩江", "梨树", "科左", "铁岭", "凤城"]
            county_filtered = df[df['City'].isin(counties)]
            print(f"5个县城数据行数: {len(county_filtered)}")
            
            # 测试年份+县城筛选
            year_county_1980 = df[(df['year'] == 1980) & (df['City'].isin(counties))]
            print(f"1980年5个县城数据行数: {len(year_county_1980)}")
            
            year_county_2023 = df[(df['year'] == 2023) & (df['City'].isin(counties))]
            print(f"2023年5个县城数据行数: {len(year_county_2023)}")
            
            # 检查黑土层厚度
            if '黑土层厚度' in df.columns:
                print(f"\n=== 黑土层厚度缺失情况 ===")
                
                # 1980年黑土层厚度缺失情况
                data_1980 = df[(df['year'] == 1980) & (df['City'].isin(counties))]
                missing_1980 = data_1980['黑土层厚度'].isna().sum()
                total_1980 = len(data_1980)
                print(f"1980年: {missing_1980}/{total_1980} 缺失")
                
                # 2023年黑土层厚度缺失情况
                data_2023 = df[(df['year'] == 2023) & (df['City'].isin(counties))]
                missing_2023 = data_2023['黑土层厚度'].isna().sum()
                total_2023 = len(data_2023)
                print(f"2023年: {missing_2023}/{total_2023} 缺失")
                
                # 按深度层分析
                if 'Depth' in df.columns:
                    print(f"\n按深度层分析1980年黑土层厚度:")
                    for depth in sorted(data_1980['Depth'].unique()):
                        depth_data = data_1980[data_1980['Depth'] == depth]
                        missing = depth_data['黑土层厚度'].isna().sum()
                        total = len(depth_data)
                        print(f"  深度{depth}: {missing}/{total} 缺失")
                    
                    print(f"\n按深度层分析2023年黑土层厚度:")
                    for depth in sorted(data_2023['Depth'].unique()):
                        depth_data = data_2023[data_2023['Depth'] == depth]
                        missing = depth_data['黑土层厚度'].isna().sum()
                        total = len(depth_data)
                        print(f"  深度{depth}: {missing}/{total} 缺失")
            else:
                print("\n错误：找不到'黑土层厚度'列！")
                soil_cols = [col for col in df.columns if '黑土' in col or '厚度' in col]
                print(f"可能的黑土层相关列: {soil_cols}")

except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
