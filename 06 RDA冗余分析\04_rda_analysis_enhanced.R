# =============================================================================
# 增强的RDA分析模块 - 提升解释度
# =============================================================================

# 增强的RDA分析函数
perform_enhanced_rda <- function(processed_data, use_county = FALSE) {
  cat("\n=== 增强RDA分析 ===\n")
  
  # 1. 数据预处理增强
  response_matrix <- processed_data$data[processed_data$response_vars]
  response_matrix <- response_matrix[complete.cases(response_matrix), ]
  
  explanatory_matrix <- processed_data$explanatory_matrix[rownames(response_matrix), ]
  
  # 2. 特征工程 - 创建交互项
  cat("🔧 创建重要交互项...\n")
  
  # 气候-地形交互
  if("Total_Precipitation_Mean" %in% names(explanatory_matrix) && 
     "Terrain_Ruggedness_Index" %in% names(explanatory_matrix)) {
    explanatory_matrix$Climate_Terrain_Interaction <- 
      scale(explanatory_matrix$Total_Precipitation_Mean)[,1] * 
      scale(explanatory_matrix$Terrain_Ruggedness_Index)[,1]
  }
  
  # 土壤-植被交互
  if("Built_Up" %in% names(explanatory_matrix) && 
     "NPP" %in% names(explanatory_matrix)) {
    explanatory_matrix$Urban_Vegetation_Interaction <- 
      scale(explanatory_matrix$Built_Up)[,1] * 
      scale(explanatory_matrix$NPP)[,1]
  }
  
  # 3. 变量重要性预筛选
  cat("🎯 基于相关性预筛选变量...\n")
  
  # 计算每个解释变量与响应变量的相关性
  numeric_explanatory <- explanatory_matrix[sapply(explanatory_matrix, is.numeric)]
  
  if(ncol(numeric_explanatory) > 0) {
    correlations <- cor(numeric_explanatory, response_matrix, use = "complete.obs")
    max_correlations <- apply(abs(correlations), 1, max, na.rm = TRUE)
    
    # 保留相关性较高的变量
    important_vars <- names(max_correlations)[max_correlations > 0.1]
    
    # 保留所有因子变量和重要的数值变量
    factor_vars <- names(explanatory_matrix)[sapply(explanatory_matrix, is.factor)]
    selected_vars <- unique(c(important_vars, factor_vars))
    
    explanatory_matrix <- explanatory_matrix[, selected_vars, drop = FALSE]
    
    cat("   保留变量数:", length(selected_vars), "\n")
  }
  
  # 4. 执行分层RDA（如果样本量足够）
  if(nrow(response_matrix) > 50) {
    cat("🔍 执行分层RDA分析...\n")
    
    # 基于土壤类型分层
    if("Soilclass-2023" %in% names(processed_data$data)) {
      soil_types <- processed_data$data[rownames(response_matrix), "Soilclass-2023"]
      
      # 为每个土壤类型执行RDA
      soil_rda_results <- list()
      
      for(soil_type in unique(soil_types)) {
        if(sum(soil_types == soil_type) >= 10) {  # 至少10个样本
          subset_idx <- soil_types == soil_type
          
          subset_response <- response_matrix[subset_idx, ]
          subset_explanatory <- explanatory_matrix[subset_idx, ]
          
          # 移除常量列
          constant_cols <- sapply(subset_explanatory, function(x) {
            if(is.numeric(x)) return(var(x, na.rm = TRUE) == 0)
            if(is.factor(x)) return(nlevels(droplevels(x)) <= 1)
            return(FALSE)
          })
          
          if(any(constant_cols)) {
            subset_explanatory <- subset_explanatory[, !constant_cols, drop = FALSE]
          }
          
          if(ncol(subset_explanatory) > 0) {
            tryCatch({
              soil_rda <- rda(subset_response ~ ., data = subset_explanatory)
              soil_rda_results[[soil_type]] <- soil_rda
              
              r2 <- RsquareAdj(soil_rda)$r.squared
              cat("   ", soil_type, "土壤 R² =", round(r2, 3), "\n")
              
            }, error = function(e) {
              cat("   ", soil_type, "土壤分析失败\n")
            })
          }
        }
      }
    }
  }
  
  # 5. 主RDA分析（使用优化参数）
  cat("🚀 执行主RDA分析...\n")
  
  # 使用更严格的前向选择
  null_model <- rda(response_matrix ~ 1, data = explanatory_matrix)
  full_model <- rda(response_matrix ~ ., data = explanatory_matrix)
  
  # 分步前向选择，更严格的标准
  forward_model <- ordiR2step(
    null_model,
    scope = formula(full_model),
    direction = "forward",
    R2scope = TRUE,
    pstep = 0.05,  # 更严格的p值
    trace = TRUE
  )
  
  # 6. 模型诊断和优化
  cat("🔬 模型诊断...\n")
  
  # 检查残差
  residuals_rda <- residuals(forward_model)
  
  # 异常值检测
  outliers <- which(abs(scale(residuals_rda[,1])) > 3)
  if(length(outliers) > 0) {
    cat("⚠️ 发现", length(outliers), "个潜在异常值\n")
  }
  
  # 7. 计算最终结果
  final_r2 <- RsquareAdj(forward_model)
  perm_test <- anova(forward_model, permutations = 999)
  
  cat("📈 最终结果:\n")
  cat("   总R² =", round(final_r2$r.squared, 4), "\n")
  cat("   调整R² =", round(final_r2$adj.r.squared, 4), "\n")
  cat("   显著性 p =", perm_test$`Pr(>F)`[1], "\n")
  
  return(list(
    model = forward_model,
    r2 = final_r2,
    perm_test = perm_test,
    soil_results = if(exists("soil_rda_results")) soil_rda_results else NULL,
    outliers = if(exists("outliers")) outliers else NULL
  ))
}