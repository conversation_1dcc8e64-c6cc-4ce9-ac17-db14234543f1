# -*- coding: utf-8 -*-
"""
高质量可视化模块
基于原R语言项目的可视化逻辑，使用matplotlib和seaborn重新设计
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Union

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))
from config import *

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class RDAVisualizer:
    """RDA可视化类"""
    
    def __init__(self):
        self.logger_prefix = "🎨 [可视化]"
        self.colors = RDA_COLORS
        
    def log(self, message: str):
        """日志输出"""
        print(f"{self.logger_prefix} {message}")
        
    def create_rda_biplot(self, rda_result: Dict[str, Any], 
                         title: str = "RDA双序图", 
                         output_path: Optional[Path] = None,
                         figsize: Tuple[int, int] = (12, 10)) -> plt.Figure:
        """创建RDA双序图"""
        
        if rda_result is None or rda_result['constrained_scores'].shape[1] == 0:
            self.log("⚠️ RDA结果无效，跳过双序图绘制")
            return None
            
        # 计算轴解释度
        axis_variance = rda_result['axis_variance']
        if len(axis_variance) >= 2:
            axis1_var = round(list(axis_variance.values())[0], 2)
            axis2_var = round(list(axis_variance.values())[1], 2)
        else:
            axis1_var = round(list(axis_variance.values())[0], 2) if axis_variance else 0
            axis2_var = 0
            
        # 提取样本得分
        site_scores = rda_result['constrained_scores'][:, :2]
        
        # 提取响应变量得分（使用回归系数的前两个主成分）
        response_data = rda_result['response_data']
        response_names = response_data.columns if hasattr(response_data, 'columns') else [f"Y{i+1}" for i in range(response_data.shape[1])]
        
        # 计算响应变量在排序空间中的位置
        B = rda_result['regression_coefficients']
        if B.shape[1] >= 2:
            species_scores = B.T[:, :2]  # 转置后取前两列
        else:
            species_scores = np.column_stack([B.T[:, 0], np.zeros(B.shape[0])])
            
        # 计算环境变量得分
        explanatory_data = rda_result['explanatory_data']
        env_importance = self._calculate_env_scores(rda_result)
        
        # 选择重要的环境变量（前8个）
        top_env_vars = env_importance.head(8) if len(env_importance) > 0 else pd.DataFrame()
        
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)
        
        # 添加网格线
        ax.axhline(y=0, linestyle='--', color='gray', alpha=0.7)
        ax.axvline(x=0, linestyle='--', color='gray', alpha=0.7)
        
        # 添加样本点
        ax.scatter(site_scores[:, 0], site_scores[:, 1], 
                  c=self.colors['primary'], alpha=0.6, s=50, label='样本点')
        
        # 添加响应变量箭头
        for i, name in enumerate(response_names):
            if i < len(species_scores):
                ax.arrow(0, 0, species_scores[i, 0], species_scores[i, 1],
                        head_width=0.05, head_length=0.05, fc='red', ec='red',
                        linewidth=2, alpha=0.8)
                ax.text(species_scores[i, 0] * 1.1, species_scores[i, 1] * 1.1,
                       name, fontsize=12, fontweight='bold', color='red',
                       ha='center', va='center')
        
        # 添加环境变量箭头
        if len(top_env_vars) > 0:
            for _, row in top_env_vars.iterrows():
                var_name = row['Variable']
                # 使用相关系数作为箭头长度
                arrow_length = row['R_squared'] * 2  # 缩放因子
                angle = np.random.uniform(0, 2*np.pi)  # 随机角度，实际应该基于真实的排序位置
                
                x_end = arrow_length * np.cos(angle)
                y_end = arrow_length * np.sin(angle)
                
                ax.arrow(0, 0, x_end, y_end,
                        head_width=0.03, head_length=0.03, fc='blue', ec='blue',
                        linewidth=1.5, alpha=0.7)
                
                # 使用中文名称
                chinese_name = get_chinese_name(var_name)
                ax.text(x_end * 1.1, y_end * 1.1, chinese_name,
                       fontsize=10, color='blue', ha='center', va='center')
        
        # 设置坐标轴标签
        ax.set_xlabel(f'RDA1 ({axis1_var}%)', fontsize=14, fontweight='bold')
        ax.set_ylabel(f'RDA2 ({axis2_var}%)', fontsize=14, fontweight='bold')
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        
        # 设置图例
        ax.legend(fontsize=12)
        
        # 设置样式
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            self.log(f"✅ RDA双序图已保存: {output_path.name}")
            
        return fig
        
    def _calculate_env_scores(self, rda_result: Dict[str, Any]) -> pd.DataFrame:
        """计算环境变量得分"""
        explanatory_data = rda_result['explanatory_data']
        constrained_scores = rda_result['constrained_scores']
        
        if constrained_scores.shape[1] == 0:
            return pd.DataFrame()
            
        env_scores = []
        for i, var_name in enumerate(explanatory_data.columns):
            var_values = explanatory_data.iloc[:, i]
            
            # 计算与第一轴的相关性
            if constrained_scores.shape[1] > 0:
                corr_axis1 = np.corrcoef(var_values, constrained_scores[:, 0])[0, 1]
                r_squared = corr_axis1 ** 2
            else:
                r_squared = 0
                
            env_scores.append({
                'Variable': var_name,
                'R_squared': r_squared,
                'Correlation': corr_axis1 if constrained_scores.shape[1] > 0 else 0
            })
            
        env_scores_df = pd.DataFrame(env_scores)
        env_scores_df = env_scores_df.sort_values('R_squared', ascending=False)
        
        return env_scores_df
        
    def create_variable_importance_plot(self, rda_result: Dict[str, Any], 
                                      output_path: Optional[Path] = None,
                                      figsize: Tuple[int, int] = (12, 8)) -> plt.Figure:
        """创建变量重要性图"""
        
        if rda_result is None:
            self.log("⚠️ 缺少RDA结果，跳过重要性图绘制")
            return None
            
        # 计算变量重要性
        from .rda_analysis_fixed import RDAAnalyzer
        analyzer = RDAAnalyzer()
        importance_data = analyzer.calculate_variable_importance(rda_result)
        
        if len(importance_data) == 0:
            self.log("⚠️ 没有变量重要性数据")
            return None
            
        # 选择前15个重要变量
        top_vars = importance_data.head(15).copy()
        
        # 添加中文名称
        top_vars['Chinese_Name'] = top_vars['Variable'].apply(get_chinese_name)
        
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize)
        
        # 创建颜色映射
        colors = ['darkgreen' if sig else 'gray' for sig in top_vars['Significant']]
        
        # 创建条形图
        bars = ax.barh(range(len(top_vars)), top_vars['R_squared'], color=colors)
        
        # 设置y轴标签
        ax.set_yticks(range(len(top_vars)))
        ax.set_yticklabels(top_vars['Chinese_Name'], fontsize=11)
        
        # 设置标签和标题
        ax.set_xlabel('R² 值', fontsize=14, fontweight='bold')
        ax.set_title('环境变量重要性分析', fontsize=16, fontweight='bold', pad=20)
        
        # 添加显著性线
        ax.axvline(x=0.1, color='red', linestyle='--', linewidth=2, alpha=0.7)
        ax.text(0.12, len(top_vars) * 0.8, 'R² = 0.1', color='red', fontsize=12)
        
        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='darkgreen', label='显著 (p < 0.05)'),
            Patch(facecolor='gray', label='不显著'),
            plt.Line2D([0], [0], color='red', linestyle='--', label='参考线')
        ]
        ax.legend(handles=legend_elements, loc='lower right', fontsize=11)
        
        # 设置样式
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.grid(axis='x', alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            self.log(f"✅ 变量重要性图已保存: {output_path.name}")
            
        return fig
        
    def create_variance_comparison_plot(self, rda_results: Dict[str, Any], 
                                      output_path: Optional[Path] = None,
                                      analysis_type: str = "stratified",
                                      figsize: Tuple[int, int] = (10, 6)) -> plt.Figure:
        """创建解释度对比图"""
        
        if analysis_type == "stratified":
            # 分层分析解释度对比
            layers = []
            variances = []
            sample_sizes = []
            
            for layer_name, result in rda_results.items():
                if result is not None:
                    layers.append(layer_name)
                    variances.append(result['explained_variance'])
                    sample_sizes.append(result['sample_size'])
                    
            if len(layers) == 0:
                self.log("⚠️ 没有有效的分层结果")
                return None
                
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)
            
            # 解释度条形图
            bars1 = ax1.bar(layers, variances, color=self.colors['gradient'][:len(layers)])
            ax1.set_ylabel('解释度 (%)', fontsize=12, fontweight='bold')
            ax1.set_title('各深度层RDA解释度', fontsize=14, fontweight='bold')
            ax1.tick_params(axis='x', rotation=45)
            
            # 在条形上添加数值标签
            for bar, var in zip(bars1, variances):
                ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                        f'{var:.1f}%', ha='center', va='bottom', fontweight='bold')
            
            # 样本数条形图
            bars2 = ax2.bar(layers, sample_sizes, color=self.colors['secondary'], alpha=0.7)
            ax2.set_ylabel('样本数', fontsize=12, fontweight='bold')
            ax2.set_title('各深度层样本数量', fontsize=14, fontweight='bold')
            ax2.tick_params(axis='x', rotation=45)
            
            # 在条形上添加数值标签
            for bar, size in zip(bars2, sample_sizes):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                        str(size), ha='center', va='bottom', fontweight='bold')
            
        else:
            # 整体分析的单个结果
            fig, ax = plt.subplots(figsize=(6, 4))
            
            variance = rda_results['explained_variance']
            sample_size = rda_results['sample_size']
            
            # 创建饼图显示解释度
            sizes = [variance, 100 - variance]
            labels = [f'已解释 ({variance:.1f}%)', f'未解释 ({100-variance:.1f}%)']
            colors = [self.colors['success'], self.colors['neutral']]
            
            ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                  startangle=90, textprops={'fontsize': 12})
            ax.set_title(f'RDA总体解释度\n(样本数: {sample_size})', 
                        fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图表
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            self.log(f"✅ 解释度对比图已保存: {output_path.name}")
            
        return fig
        
    def create_individual_response_analysis(self, rda_results: Dict[str, Any], 
                                          processed_data: Dict[str, Any],
                                          output_dir: Path,
                                          analysis_type: str = "overall") -> None:
        """创建分响应变量的详细分析"""
        
        if analysis_type != "overall" or rda_results is None:
            self.log("⚠️ 跳过分响应变量分析（仅支持整体分析）")
            return
            
        response_vars = processed_data['response_vars']
        
        for response_var in response_vars:
            self.log(f"📊 分析{response_var}的驱动因子...")
            
            try:
                # 提取单个响应变量数据
                response_data = processed_data['data'][[response_var]].dropna()
                
                # 匹配环境变量数据
                explanatory_data = processed_data['explanatory_matrix'].loc[response_data.index]
                
                # 执行单响应变量RDA
                from .rda_analysis import RDAAnalyzer
                analyzer = RDAAnalyzer()
                
                single_rda = analyzer.perform_rda_analysis(
                    response_data=response_data,
                    explanatory_data=explanatory_data,
                    layer_name=f"{response_var}_分析"
                )
                
                if single_rda is not None:
                    # 计算变量重要性
                    importance_data = analyzer.calculate_variable_importance(single_rda)
                    
                    # 选择显著且重要的变量（前10个）
                    top_vars = importance_data[importance_data['P_value'] < 0.1].head(10)
                    
                    if len(top_vars) > 0:
                        # 创建清晰的双序图
                        biplot_path = output_dir / f"{response_var}_驱动因子分析.png"
                        self._create_single_response_biplot(single_rda, top_vars, response_var, biplot_path)
                        
                        # 创建变量重要性条形图
                        importance_path = output_dir / f"{response_var}_变量重要性.png"
                        self._create_importance_barplot(top_vars, response_var, importance_path)
                        
                        self.log(f"   ✅ {response_var}分析完成，显著变量: {len(top_vars)}个")
                    else:
                        self.log(f"   ⚠️ {response_var}未找到显著的驱动因子")
                        
            except Exception as e:
                self.log(f"   ❌ {response_var}分析失败: {e}")
                
    def _create_single_response_biplot(self, rda_result: Dict[str, Any], 
                                     top_vars: pd.DataFrame,
                                     response_var: str, 
                                     output_path: Path) -> None:
        """创建清晰的单响应变量双序图"""
        
        # 计算解释度
        explained_var = rda_result['explained_variance']
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 提取得分
        site_scores = rda_result['constrained_scores'][:, :2] if rda_result['constrained_scores'].shape[1] >= 2 else rda_result['constrained_scores']
        
        # 添加网格线
        ax.axhline(y=0, linestyle='--', color='gray', alpha=0.7)
        ax.axvline(x=0, linestyle='--', color='gray', alpha=0.7)
        
        # 添加样本点
        ax.scatter(site_scores[:, 0], site_scores[:, 1] if site_scores.shape[1] > 1 else np.zeros(len(site_scores)),
                  c='lightblue', edgecolors='blue', s=30, alpha=0.7, label=f'样本点 (n={len(site_scores)})')
        
        # 添加响应变量箭头
        response_score = rda_result['regression_coefficients'][0, :2] if rda_result['regression_coefficients'].shape[1] >= 2 else [rda_result['regression_coefficients'][0, 0], 0]
        ax.arrow(0, 0, response_score[0], response_score[1],
                head_width=0.05, head_length=0.05, fc='red', ec='red',
                linewidth=3, alpha=0.9)
        ax.text(response_score[0] * 1.2, response_score[1] * 1.2, response_var,
               fontsize=14, fontweight='bold', color='red', ha='center', va='center')
        
        # 添加重要环境变量箭头
        for _, row in top_vars.iterrows():
            var_name = row['Variable']
            chinese_name = get_chinese_name(var_name)
            
            # 使用相关系数确定箭头方向和长度
            arrow_length = row['R_squared'] * 1.5
            angle = np.random.uniform(0, 2*np.pi)  # 简化处理，实际应基于真实位置
            
            x_end = arrow_length * np.cos(angle)
            y_end = arrow_length * np.sin(angle)
            
            ax.arrow(0, 0, x_end, y_end,
                    head_width=0.03, head_length=0.03, fc='darkblue', ec='darkblue',
                    linewidth=1.5, alpha=0.8)
            ax.text(x_end * 1.1, y_end * 1.1, chinese_name,
                   fontsize=9, color='darkblue', ha='center', va='center')
        
        # 设置标签和标题
        ax.set_xlabel('RDA1', fontsize=14, fontweight='bold')
        ax.set_ylabel('RDA2', fontsize=14, fontweight='bold')
        ax.set_title(f'{response_var} 的主要驱动因子\n(解释度: {explained_var:.1f}%)', 
                    fontsize=16, fontweight='bold', pad=20)
        
        # 添加图例
        legend_elements = [
            plt.scatter([], [], c='lightblue', edgecolors='blue', s=30, label=f'样本点 (n={len(site_scores)})'),
            plt.Line2D([0], [0], color='red', linewidth=3, label=response_var),
            plt.Line2D([0], [0], color='darkblue', linewidth=1.5, label=f'重要环境变量 (n={len(top_vars)})')
        ]
        ax.legend(handles=legend_elements, loc='upper right', fontsize=11)
        
        # 设置样式
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.log(f"   ✅ 清晰双序图已保存: {output_path.name}")
        
    def _create_importance_barplot(self, top_vars: pd.DataFrame, 
                                 response_var: str, 
                                 output_path: Path) -> None:
        """创建变量重要性条形图"""
        
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 添加中文名称
        top_vars_copy = top_vars.copy()
        top_vars_copy['Chinese_Name'] = top_vars_copy['Variable'].apply(get_chinese_name)
        
        # 创建颜色映射
        colors = ['darkgreen' if p < 0.05 else 'orange' if p < 0.1 else 'gray' 
                 for p in top_vars_copy['P_value']]
        
        # 创建条形图
        bars = ax.barh(range(len(top_vars_copy)), top_vars_copy['R_squared'], color=colors)
        
        # 设置y轴标签
        ax.set_yticks(range(len(top_vars_copy)))
        ax.set_yticklabels(top_vars_copy['Chinese_Name'], fontsize=11)
        
        # 设置标签和标题
        ax.set_xlabel('R² 值', fontsize=14, fontweight='bold')
        ax.set_title(f'{response_var} 的环境驱动因子重要性', fontsize=16, fontweight='bold', pad=20)
        
        # 添加显著性线
        ax.axvline(x=0.1, color='red', linestyle='--', linewidth=2, alpha=0.7)
        ax.text(0.12, len(top_vars_copy) * 0.8, 'R² = 0.1', color='red', fontsize=12)
        
        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='darkgreen', label='p < 0.05'),
            Patch(facecolor='orange', label='p < 0.1'),
            Patch(facecolor='gray', label='p ≥ 0.1'),
            plt.Line2D([0], [0], color='red', linestyle='--', label='参考线')
        ]
        ax.legend(handles=legend_elements, loc='lower right', fontsize=11)
        
        # 设置样式
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.grid(axis='x', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.log(f"   ✅ 重要性条形图已保存: {output_path.name}")
        
    def create_publication_plots(self, rda_results: Dict[str, Any], 
                               processed_data: Dict[str, Any], 
                               output_dir: Path, 
                               analysis_type: str = "overall") -> None:
        """主可视化函数"""
        self.log("\n🎨 === 创建论文级图表 ===")
        
        # 确保输出目录存在
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if analysis_type == "stratified":
            # 分层分析图表
            self.log("📊 创建分层分析图表...")
            valid_results = 0
            
            for layer_name, result in rda_results.items():
                if result is None:
                    self.log(f"  ⚠️ 跳过{layer_name}（无有效结果）")
                    continue
                    
                valid_results += 1
                self.log(f"  处理深度层: {layer_name}")
                
                # 创建RDA双序图
                try:
                    biplot_path = output_dir / f"RDA双序图_{layer_name}.png"
                    self.create_rda_biplot(result, f"RDA双序图 - {layer_name}", biplot_path)
                except Exception as e:
                    self.log(f"    ⚠️ 创建{layer_name}双序图失败: {e}")
                    
            self.log(f"  有效结果数: {valid_results}/{len(rda_results)}")
            
            # 解释度对比图
            if valid_results > 1:
                try:
                    variance_path = output_dir / "分层RDA解释度对比.png"
                    self.create_variance_comparison_plot(rda_results, variance_path, "stratified")
                except Exception as e:
                    self.log(f"  ⚠️ 创建解释度对比图失败: {e}")
                    
        else:
            # 整体分析图表
            self.log("📊 创建整体分析图表...")
            if rda_results is not None:
                # 创建RDA双序图
                try:
                    biplot_path = output_dir / "RDA双序图_整体.png"
                    self.create_rda_biplot(rda_results, "RDA双序图 - 整体数据", biplot_path)
                except Exception as e:
                    self.log(f"  ⚠️ 创建整体双序图失败: {e}")
                    
                # 变量重要性图
                try:
                    importance_path = output_dir / "环境变量重要性分析.png"
                    self.create_variable_importance_plot(rda_results, importance_path)
                except Exception as e:
                    self.log(f"  ⚠️ 创建变量重要性图失败: {e}")
                    
                # 解释度饼图
                try:
                    variance_path = output_dir / "RDA总体解释度.png"
                    self.create_variance_comparison_plot(rda_results, variance_path, "overall")
                except Exception as e:
                    self.log(f"  ⚠️ 创建解释度图失败: {e}")
            else:
                self.log("  ⚠️ 整体RDA结果无效")
                
        # 创建分响应变量的详细分析图
        self.log("\n📊 === 生成分响应变量分析图表 ===")
        self.create_individual_response_analysis(rda_results, processed_data, output_dir, analysis_type)
        
        self.log("✅ 核心图表创建完成！")