# -*- coding: utf-8 -*-
"""
数据处理模块
基于原R语言项目的数据预处理逻辑
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))
from config import *

class DataProcessor:
    """数据处理类"""
    
    def __init__(self):
        self.logger_prefix = "📊 [数据处理]"
        
    def log(self, message: str):
        """日志输出"""
        print(f"{self.logger_prefix} {message}")
        
    def validate_config(self) -> bool:
        """配置验证函数"""
        self.log("=== 配置验证 ===")
        
        if not Path(VIF_FILE_PATH).exists():
            self.log(f"❌ VIF筛选结果文件不存在: {VIF_FILE_PATH}")
            return False
        else:
            self.log("✅ VIF筛选结果文件存在")
            
        if not Path(CHANGE_FILE_PATH).exists():
            self.log(f"❌ 变化量数据文件不存在: {CHANGE_FILE_PATH}")
            return False
        else:
            self.log("✅ 变化量数据文件存在")
            
        if not (0 <= MISSING_THRESHOLD <= 1):
            self.log("❌ 缺失值阈值必须在0-1之间")
            return False
            
        self.log("✅ 配置验证通过\n")
        return True
        
    def get_mode(self, x: pd.Series) -> Any:
        """众数函数"""
        x = x.dropna()
        if len(x) == 0:
            return np.nan
        return x.mode().iloc[0] if len(x.mode()) > 0 else np.nan
        
    def handle_missing_values(self, data: pd.DataFrame, 
                            threshold: float = MISSING_THRESHOLD,
                            numeric_method: str = NUMERIC_MISSING_METHOD,
                            categorical_method: str = CATEGORICAL_MISSING_METHOD) -> pd.DataFrame:
        """改进的缺失值处理函数"""
        self.log("=== 缺失值处理 ===")
        
        original_rows = len(data)
        
        # 计算每行的缺失值比例
        missing_per_row = data.isnull().sum(axis=1) / len(data.columns)
        rows_to_remove = missing_per_row > threshold
        
        if rows_to_remove.sum() > 0:
            self.log(f"🗑️ 删除{rows_to_remove.sum()}行缺失值比例超过{threshold*100}%的数据")
            data = data[~rows_to_remove].copy()
            
        # 分析剩余数据的缺失值情况
        missing_summary = data.isnull().sum()
        missing_cols = missing_summary[missing_summary > 0]
        
        if len(missing_cols) > 0:
            self.log("📊 剩余缺失值情况:")
            for col_name, missing_count in missing_cols.items():
                self.log(f"   {col_name}: {missing_count}个缺失值")
                
            # 处理数值型变量
            numeric_cols = data.select_dtypes(include=[np.number]).columns
            missing_numeric = missing_cols[missing_cols.index.isin(numeric_cols)]
            
            if len(missing_numeric) > 0:
                self.log("🔢 填补数值型变量:")
                for col_name in missing_numeric.index:
                    if numeric_method == "mean":
                        fill_value = data[col_name].mean()
                    else:
                        fill_value = data[col_name].median()
                    data[col_name].fillna(fill_value, inplace=True)
                    self.log(f"   {col_name}: 用{fill_value:.4f}填补")
                    
            # 处理分类型变量
            categorical_cols = data.select_dtypes(exclude=[np.number]).columns
            missing_categorical = missing_cols[missing_cols.index.isin(categorical_cols)]
            
            if len(missing_categorical) > 0:
                self.log("📝 填补分类型变量:")
                for col_name in missing_categorical.index:
                    if categorical_method == "mode":
                        fill_value = self.get_mode(data[col_name])
                        data[col_name].fillna(fill_value, inplace=True)
                        self.log(f"   {col_name}: 用'{fill_value}'填补")
        else:
            self.log("✅ 没有缺失值")
            
        final_rows = len(data)
        self.log(f"📊 数据行数变化: {original_rows} → {final_rows}\n")
        
        return data
        
    def analyze_transition_patterns(self, data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """分析转移模式"""
        self.log("🔍 分析转移模式...")
        
        # 土地利用转移分析
        landuse_1980 = data['LandUse-1980'].fillna("未知")
        landuse_2023 = data['LandUse-2023'].fillna("未知")
        
        lu_transition_matrix = pd.crosstab(landuse_1980, landuse_2023, margins=False)
        self.log("土地利用转移矩阵:")
        self.log(str(lu_transition_matrix))
        
        # 土壤类型转移分析
        soiltype_1980 = data['Soilclass-1980'].fillna("未知")
        soiltype_2023 = data['Soilclass-2023'].fillna("未知")
        
        st_transition_matrix = pd.crosstab(soiltype_1980, soiltype_2023, margins=False)
        self.log("\n土壤类型转移矩阵:")
        self.log(str(st_transition_matrix))
        
        return {
            'lu_matrix': lu_transition_matrix,
            'st_matrix': st_transition_matrix
        }
        
    def classify_variables(self, env_vars: List[str]) -> Dict[str, List[str]]:
        """变量分类函数"""
        self.log("=== 变量分类 ===")
        
        # 静态协变量：地形 + 分类变量
        static_available = [var for var in STATIC_VARS if var in env_vars]
        # 动态协变量：气候 + 植被 + 人类活动 + 3D土壤
        dynamic_available = [var for var in DYNAMIC_VARS if var in env_vars]
        unclassified = [var for var in env_vars if var not in static_available + dynamic_available]
        
        self.log(f"📊 静态协变量({len(static_available)}个): {', '.join(static_available)}")
        self.log(f"📈 动态协变量({len(dynamic_available)}个): {', '.join(dynamic_available)}")
        
        if unclassified:
            self.log(f"❓ 未分类变量: {', '.join(unclassified)}")
            dynamic_available.extend(unclassified)
            
        return {
            'static': static_available,
            'dynamic': dynamic_available,
            'groups': VARIABLE_GROUPS
        }
        
    def preprocess_data(self, vif_data: pd.DataFrame, 
                       change_data: pd.DataFrame, 
                       response_vars: List[str]) -> Dict[str, Any]:
        """数据预处理主函数"""
        self.log("=== 数据预处理 ===")
        
        # VIF筛选后的环境变量
        env_cols = [col for col in vif_data.columns 
                   if col not in BASE_COLS + response_vars]
        
        # 构建最终数据集
        final_cols = BASE_COLS + response_vars + env_cols
        available_cols = [col for col in final_cols if col in change_data.columns]
        processed_data = change_data[available_cols].copy()
        
        self.log(f"📊 数据维度: {len(processed_data)}行 × {len(processed_data.columns)}列")
        self.log(f"🎯 响应变量: {', '.join(response_vars)}")
        self.log(f"🌍 环境变量数量: {len(env_cols)}")
        
        # 处理缺失值
        processed_data = self.handle_missing_values(processed_data)
        
        return {
            'data': processed_data,
            'response_vars': response_vars,
            'env_vars': env_cols,
            'base_vars': BASE_COLS
        }
        
    def preprocess_data_with_transitions(self, vif_data: pd.DataFrame, 
                                       change_data: pd.DataFrame, 
                                       response_vars: List[str], 
                                       create_transitions: bool = True) -> Dict[str, Any]:
        """数据预处理函数（包含转移变量）"""
        self.log("=== 数据预处理（含转移变量）===")
        
        # VIF筛选后的环境变量
        env_cols = [col for col in vif_data.columns 
                   if col not in BASE_COLS + response_vars]
        
        # 构建最终数据集
        final_cols = BASE_COLS + response_vars + env_cols
        available_cols = [col for col in final_cols if col in change_data.columns]
        processed_data = change_data[available_cols].copy()
        
        self.log(f"📊 数据维度: {len(processed_data)}行 × {len(processed_data.columns)}列")
        self.log(f"🎯 响应变量: {', '.join(response_vars)}")
        self.log(f"🌍 环境变量数量: {len(env_cols)}")
        
        # 处理缺失值
        processed_data = self.handle_missing_values(processed_data)
        
        # 准备环境变量矩阵
        explanatory_vars = processed_data[env_cols].copy()
        
        # 创建转移变量
        transition_patterns = None
        if create_transitions:
            self.log("\n🔄 创建转移变量...")
            
            # 分析转移模式
            transition_patterns = self.analyze_transition_patterns(processed_data)
            
            # 创建简化的转移变量（分类变量而不是多个0/1变量）
            self.log("🔄 创建简化转移变量...")
            
            # 土地利用转移（作为分类变量）
            landuse_1980 = processed_data['LandUse-1980'].fillna("未知")
            landuse_2023 = processed_data['LandUse-2023'].fillna("未知")
            landuse_transition = landuse_1980 + "→" + landuse_2023
            
            # 土壤类型转移（作为分类变量）
            soiltype_1980 = processed_data['Soilclass-1980'].fillna("未知")
            soiltype_2023 = processed_data['Soilclass-2023'].fillna("未知")
            soiltype_transition = soiltype_1980 + "→" + soiltype_2023
            
            # 创建转移变量数据框
            all_transitions = pd.DataFrame({
                'LandUse_Transition': pd.Categorical(landuse_transition),
                'SoilType_Transition': pd.Categorical(soiltype_transition)
            }, index=processed_data.index)
            
            self.log(f"   土地利用转移类型: {all_transitions['LandUse_Transition'].nunique()}种")
            self.log(f"   土壤类型转移类型: {all_transitions['SoilType_Transition'].nunique()}种")
            self.log("   转移变量将作为因子变量输入RDA（自动处理虚拟变量）")
            
            # 移除常量列
            constant_cols = [col for col in all_transitions.columns 
                           if all_transitions[col].nunique() <= 1]
            if constant_cols:
                self.log(f"   移除{len(constant_cols)}个常量变量")
                all_transitions = all_transitions.drop(columns=constant_cols)
                
            # 合并到协变量矩阵
            explanatory_vars = pd.concat([explanatory_vars, all_transitions], axis=1)
            self.log(f"   最终协变量矩阵: {len(explanatory_vars)}行 × {len(explanatory_vars.columns)}列")
            
        # 更新环境变量列表，排除响应变量和ID列
        actual_env_vars = [col for col in processed_data.columns 
                          if col not in response_vars + ["ID", "Depth_Mid", "City", "County"]]
        
        return {
            'data': processed_data,
            'response_vars': response_vars,
            'env_vars': actual_env_vars,
            'base_vars': BASE_COLS,
            'explanatory_matrix': explanatory_vars,
            'transition_patterns': transition_patterns
        }
        
    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """加载数据文件"""
        self.log("=== 数据加载 ===")
        
        try:
            vif_data = pd.read_excel(VIF_FILE_PATH, sheet_name="筛选后数据")
            self.log(f"✅ VIF数据加载成功: {len(vif_data)}行 × {len(vif_data.columns)}列")
        except Exception as e:
            self.log(f"❌ VIF数据加载失败: {e}")
            raise
            
        try:
            change_data = pd.read_excel(CHANGE_FILE_PATH)
            self.log(f"✅ 变化量数据加载成功: {len(change_data)}行 × {len(change_data.columns)}列")
        except Exception as e:
            self.log(f"❌ 变化量数据加载失败: {e}")
            raise
            
        return vif_data, change_data