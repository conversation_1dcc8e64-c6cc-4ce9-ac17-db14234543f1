{"explorer.confirmDelete": false, "git.openRepositoryInParentFolders": "never", "github.copilot.enable": {"*": false, "plaintext": false, "markdown": false, "scminput": false, "r": false}, "git.enableSmartCommit": true, "debug.terminal.clearBeforeReusing": true, "editor.mouseWheelZoom": true, "extensions.ignoreRecommendations": true, "explorer.confirmDragAndDrop": false, "diffEditor.hideUnchangedRegions.enabled": true, "[python]": {"diffEditor.ignoreTrimWhitespace": false}, "window.commandCenter": true, "workbench.activityBar.orientation": "vertical", "cursor.general.disableHttp2": true, "update.mode": "none", "http.proxy": "http://127.0.0.1:7890", "diffEditor.maxComputationTime": 0, "workbench.colorTheme": "Default Dark+"}