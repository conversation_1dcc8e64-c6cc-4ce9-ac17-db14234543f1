# =============================================================================
# 优化的数据处理模块 - 解决多重共线性问题
# =============================================================================

# 改进的转移变量创建函数
create_optimized_transition_variables <- function(data) {
  cat("🔄 创建优化的转移变量（解决多重共线性）...\n")
  
  # 1. 创建土地利用转移变量（简化版）
  landuse_transitions <- paste(data$`LandUse-1980`, "→", data$`LandUse-2023`, sep="")
  
  # 合并低频转移类型，减少共线性
  transition_counts <- table(landuse_transitions)
  rare_transitions <- names(transition_counts)[transition_counts < 5]  # 少于5个样本的转移类型
  
  landuse_transitions_simplified <- landuse_transitions
  landuse_transitions_simplified[landuse_transitions %in% rare_transitions] <- "其他转移"
  
  data$LandUse_Transition_Simplified <- as.factor(landuse_transitions_simplified)
  
  # 2. 创建土壤类型转移变量（二元化处理）
  soiltype_transitions <- paste(data$`Soilclass-1980`, "→", data$`Soilclass-2023`, sep="")
  
  # 创建关键的二元转移指标，避免过多类别
  data$SoilType_Stable <- as.factor(ifelse(data$`Soilclass-1980` == data$`Soilclass-2023`, "稳定", "变化"))
  data$SoilType_To_Black <- as.factor(ifelse(data$`Soilclass-2023` == "黑土", "转为黑土", "其他"))
  data$SoilType_From_Black <- as.factor(ifelse(data$`Soilclass-1980` == "黑土", "来自黑土", "其他"))
  
  # 3. 创建数值型转移强度指标
  # 土地利用变化强度（基于生态系统服务价值）
  landuse_values <- c("旱地"=1, "林地"=3, "草地"=2, "沼泽地"=4, "城镇用地"=0, "农村居民点"=0.5)
  
  data$LandUse_Change_Intensity <- abs(
    landuse_values[data$`LandUse-2023`] - landuse_values[data$`LandUse-1980`]
  )
  
  # 土壤质量变化指标（基于土壤肥力）
  soil_quality <- c("黑土"=4, "草甸土"=3, "暗棕壤"=2, "沼泽土"=1)
  
  data$Soil_Quality_Change <- 
    soil_quality[data$`Soilclass-2023`] - soil_quality[data$`Soilclass-1980`]
  
  cat("✅ 优化转移变量创建完成:\n")
  cat("   - 土地利用转移（简化）:", length(unique(data$LandUse_Transition_Simplified)), "类别\n")
  cat("   - 土壤稳定性指标: 2类别\n")
  cat("   - 土壤转移方向指标: 各2类别\n")
  cat("   - 数值型变化强度指标: 2个\n")
  
  return(data)
}

# VIF检验函数
check_multicollinearity <- function(explanatory_matrix, threshold = 10) {
  cat("🔍 检查多重共线性...\n")
  
  # 只对数值型变量计算VIF
  numeric_vars <- explanatory_matrix[sapply(explanatory_matrix, is.numeric)]
  
  if(ncol(numeric_vars) > 1) {
    library(car)
    
    # 创建线性模型用于VIF计算
    temp_response <- rnorm(nrow(numeric_vars))  # 临时响应变量
    temp_model <- lm(temp_response ~ ., data = numeric_vars)
    
    vif_values <- vif(temp_model)
    high_vif <- vif_values[vif_values > threshold]
    
    if(length(high_vif) > 0) {
      cat("⚠️ 发现高VIF变量:\n")
      print(high_vif)
      
      # 移除高VIF变量
      remove_vars <- names(high_vif)
      explanatory_matrix <- explanatory_matrix[, !names(explanatory_matrix) %in% remove_vars]
      cat("🗑️ 已移除高VIF变量:", paste(remove_vars, collapse = ", "), "\n")
    } else {
      cat("✅ 无严重多重共线性问题\n")
    }
  }
  
  return(explanatory_matrix)
}