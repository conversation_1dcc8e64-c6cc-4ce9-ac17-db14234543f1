# -*- coding: utf-8 -*-
"""
结果导出模块
基于原R语言项目的结果导出逻辑
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
from typing import Dict, List, Tuple, Optional, Any, Union
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))
from config import *

class ResultsExporter:
    """结果导出类"""
    
    def __init__(self):
        self.logger_prefix = "💾 [结果导出]"
        
    def log(self, message: str):
        """日志输出"""
        print(f"{self.logger_prefix} {message}")
        
    def export_rda_results(self, rda_results: Dict[str, Any], 
                          processed_data: Dict[str, Any], 
                          output_dir: Path, 
                          analysis_type: str = "overall") -> None:
        """导出RDA分析结果"""
        self.log("\n💾 === 导出分析结果 ===")
        
        # 检查输入
        if rda_results is None:
            self.log("⚠️ RDA结果为空，跳过导出")
            return
            
        # 确保输出目录存在
        output_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # 创建文本报告和Excel报告
            if analysis_type == "stratified":
                self._export_stratified_results(rda_results, processed_data, output_dir)
            else:
                self._export_overall_results(rda_results, processed_data, output_dir)
                
        except Exception as e:
            self.log(f"⚠️ 导出过程中出现错误: {e}")
            self.log("跳过详细导出，继续分析...")
            
        self.log("✅ 结果导出完成")
        
    def _export_overall_results(self, rda_result: Dict[str, Any], 
                               processed_data: Dict[str, Any], 
                               output_dir: Path) -> None:
        """导出整体分析结果"""
        
        # 创建文本摘要
        report_lines = [
            "=== RDA整体分析摘要 ===",
            f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            f"样本数: {rda_result['sample_size']}",
            f"解释度: {rda_result['explained_variance']:.2f}%",
            f"显著性P值: {rda_result['overall_pvalue']:.4f}",
            f"约束轴数: {len(rda_result['constrained_eigenvals'])}",
            f"非约束轴数: {len(rda_result['unconstrained_eigenvals'])}",
            ""
        ]
        
        # 添加轴解释度信息
        if rda_result['axis_variance']:
            report_lines.append("各轴解释度:")
            for axis_name, variance in rda_result['axis_variance'].items():
                report_lines.append(f"  {axis_name}: {variance:.2f}%")
            report_lines.append("")
            
        # 保存文本报告
        report_file = output_dir / "RDA分析摘要.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        self.log(f"✅ 分析摘要已保存: {report_file.name}")
        
        # 创建详细的Excel报告
        excel_file = output_dir / "RDA分析结果.xlsx"
        self._create_comprehensive_excel_report(rda_result, excel_file)
        
    def _export_stratified_results(self, rda_results: Dict[str, Any], 
                                  processed_data: Dict[str, Any], 
                                  output_dir: Path) -> None:
        """导出分层分析结果"""
        
        # 创建文本摘要
        report_lines = [
            "=== RDA分层分析摘要 ===",
            f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            ""
        ]
        
        for layer_name, result in rda_results.items():
            if result is not None:
                report_lines.extend([
                    f"深度层: {layer_name}",
                    f"  样本数: {result['sample_size']}",
                    f"  解释度: {result['explained_variance']:.2f}%",
                    f"  显著性P值: {result['overall_pvalue']:.4f}",
                    ""
                ])
                
        # 保存文本报告
        report_file = output_dir / "RDA分析摘要.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        self.log(f"✅ 分析摘要已保存: {report_file.name}")
        
        # 创建详细的Excel报告
        excel_file = output_dir / "RDA分析结果.xlsx"
        self._create_stratified_excel_report(rda_results, excel_file)
        
    def _create_comprehensive_excel_report(self, rda_result: Dict[str, Any], 
                                         excel_file: Path) -> None:
        """创建详细的Excel报告（论文级）"""
        
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            
            # 1. 基本统计摘要
            basic_stats = pd.DataFrame({
                '项目': [
                    '分析类型', '样本数量', '响应变量数', '环境变量数',
                    '总解释度(%)', '显著性P值', '约束轴数', '非约束轴数'
                ],
                '值': [
                    '冗余分析(RDA)',
                    rda_result['sample_size'],
                    rda_result['response_data'].shape[1],
                    rda_result['variable_count'],
                    round(rda_result['explained_variance'], 3),
                    round(rda_result['overall_pvalue'], 4),
                    len(rda_result['constrained_eigenvals']),
                    len(rda_result['unconstrained_eigenvals'])
                ]
            })
            basic_stats.to_excel(writer, sheet_name='1-基本统计', index=False)
            
            # 2. 轴解释度详细信息
            if rda_result['axis_variance']:
                axis_details = pd.DataFrame({
                    '排序轴': list(rda_result['axis_variance'].keys()),
                    '解释度_百分比': [round(v, 3) for v in rda_result['axis_variance'].values()]
                })
                
                # 计算累积解释度
                axis_details['累积解释度'] = axis_details['解释度_百分比'].cumsum()
                axis_details.to_excel(writer, sheet_name='2-轴解释度', index=False)
            
            # 3. 环境变量重要性分析
            try:
                from .rda_analysis import RDAAnalyzer
                analyzer = RDAAnalyzer()
                importance_data = analyzer.calculate_variable_importance(rda_result)
                
                if len(importance_data) > 0:
                    # 添加中文名称和显著性标记
                    importance_data['环境变量_中文'] = importance_data['Variable'].apply(get_chinese_name)
                    importance_data['显著性'] = importance_data['P_value'].apply(
                        lambda p: "***" if p < 0.001 else "**" if p < 0.01 else "*" if p < 0.05 else "ns"
                    )
                    importance_data['重要性等级'] = importance_data['R_squared'].apply(
                        lambda r: "高" if r > 0.3 else "中" if r > 0.1 else "低"
                    )
                    
                    # 重新排列列
                    var_importance = importance_data[[
                        'Variable', '环境变量_中文', 'R_squared', 'P_value', '显著性', '重要性等级'
                    ]].copy()
                    var_importance.columns = ['环境变量_英文', '环境变量_中文', 'R平方', 'P值', '显著性', '重要性等级']
                    
                    var_importance.to_excel(writer, sheet_name='3-变量重要性', index=False)
                    
            except Exception as e:
                self.log(f"⚠️ 环境变量重要性分析失败: {e}")
            
            # 4. 样本得分
            site_scores = rda_result['constrained_scores']
            max_axes = min(4, site_scores.shape[1])
            
            if max_axes > 0:
                site_scores_df = pd.DataFrame(
                    site_scores[:, :max_axes],
                    columns=[f'RDA{i+1}' for i in range(max_axes)]
                )
                site_scores_df.insert(0, '样本ID', range(1, len(site_scores_df) + 1))
                site_scores_df.to_excel(writer, sheet_name='4-样本得分', index=False)
            
            # 5. 响应变量得分
            response_data = rda_result['response_data']
            if hasattr(response_data, 'columns'):
                response_names = response_data.columns.tolist()
            else:
                response_names = [f'Y{i+1}' for i in range(response_data.shape[1])]
                
            # 使用回归系数作为响应变量得分
            B = rda_result['regression_coefficients']
            max_axes = min(4, B.shape[1])
            
            if max_axes > 0:
                species_scores_df = pd.DataFrame(
                    B[:, :max_axes],
                    columns=[f'RDA{i+1}' for i in range(max_axes)]
                )
                species_scores_df.insert(0, '响应变量', response_names)
                species_scores_df.to_excel(writer, sheet_name='5-响应变量得分', index=False)
            
            # 6. 环境变量系数
            explanatory_data = rda_result['explanatory_data']
            if max_axes > 0 and len(explanatory_data.columns) > 0:
                # 计算环境变量与排序轴的相关系数
                env_correlations = []
                constrained_scores = rda_result['constrained_scores']
                
                for i, var_name in enumerate(explanatory_data.columns):
                    var_values = explanatory_data.iloc[:, i]
                    correlations = []
                    
                    for axis in range(max_axes):
                        if axis < constrained_scores.shape[1]:
                            corr = np.corrcoef(var_values, constrained_scores[:, axis])[0, 1]
                            correlations.append(corr)
                        else:
                            correlations.append(0)
                            
                    env_correlations.append([var_name] + correlations)
                
                env_scores_df = pd.DataFrame(
                    env_correlations,
                    columns=['环境变量'] + [f'RDA{i+1}' for i in range(max_axes)]
                )
                
                # 添加中文名称
                env_scores_df['环境变量_中文'] = env_scores_df['环境变量'].apply(get_chinese_name)
                
                # 重新排列列
                cols = ['环境变量', '环境变量_中文'] + [f'RDA{i+1}' for i in range(max_axes)]
                env_scores_df = env_scores_df[cols]
                
                env_scores_df.to_excel(writer, sheet_name='6-环境变量系数', index=False)
        
        self.log(f"✅ 详细Excel报告已保存: {excel_file.name}")
        
    def _create_stratified_excel_report(self, rda_results: Dict[str, Any], 
                                      excel_file: Path) -> None:
        """创建分层分析的Excel报告"""
        
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            
            # 1. 分层结果汇总
            summary_data = []
            for layer_name, result in rda_results.items():
                if result is not None:
                    summary_data.append({
                        '深度层': layer_name,
                        '样本数': result['sample_size'],
                        '解释度_百分比': round(result['explained_variance'], 3),
                        '显著性P值': round(result['overall_pvalue'], 4),
                        '约束轴数': len(result['constrained_eigenvals']),
                        '非约束轴数': len(result['unconstrained_eigenvals'])
                    })
                    
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='1-分层结果汇总', index=False)
            
            # 2. 为每个深度层创建详细工作表
            for layer_name, result in rda_results.items():
                if result is not None:
                    self._create_layer_detail_sheet(writer, result, layer_name)
                    
        self.log(f"✅ 已创建分层分析详细报告: {excel_file.name}")
        
    def _create_layer_detail_sheet(self, writer: pd.ExcelWriter, 
                                 rda_result: Dict[str, Any], 
                                 layer_name: str) -> None:
        """为单个深度层创建详细工作表"""
        
        sheet_name = f'详细-{layer_name}'
        
        # 基本统计
        basic_stats = pd.DataFrame({
            '项目': ['深度层', '样本数', '解释度(%)', 'P值', '约束轴数'],
            '值': [
                layer_name,
                rda_result['sample_size'],
                round(rda_result['explained_variance'], 3),
                round(rda_result['overall_pvalue'], 4),
                len(rda_result['constrained_eigenvals'])
            ]
        })
        
        # 写入基本统计（从第1行开始）
        basic_stats.to_excel(writer, sheet_name=sheet_name, startrow=0, index=False)
        
        # 轴解释度（从第8行开始）
        if rda_result['axis_variance']:
            axis_details = pd.DataFrame({
                '排序轴': list(rda_result['axis_variance'].keys()),
                '解释度_百分比': [round(v, 3) for v in rda_result['axis_variance'].values()]
            })
            
            # 写入轴解释度标题
            pd.DataFrame({'项目': ['轴解释度'], '值': ['']}).to_excel(
                writer, sheet_name=sheet_name, startrow=7, index=False, header=False
            )
            
            # 写入轴解释度数据
            axis_details.to_excel(writer, sheet_name=sheet_name, startrow=8, index=False)
            
    def create_summary_report(self, rda_results: Dict[str, Any], 
                            processed_data: Dict[str, Any], 
                            output_dir: Path, 
                            analysis_type: str = "overall") -> None:
        """创建分析摘要报告"""
        
        self.log("📋 创建分析摘要报告...")
        
        # 创建Markdown格式的摘要报告
        report_lines = [
            "# RDA冗余分析结果摘要",
            "",
            f"**分析时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}",
            f"**分析类型**: {'分层RDA分析' if analysis_type == 'stratified' else '整体RDA分析'}",
            ""
        ]
        
        if analysis_type == "stratified":
            report_lines.extend([
                "## 分层分析结果",
                "",
                "| 深度层 | 样本数 | 解释度(%) | P值 | 显著性 |",
                "|--------|--------|-----------|-----|--------|"
            ])
            
            for layer_name, result in rda_results.items():
                if result is not None:
                    significance = "***" if result['overall_pvalue'] < 0.001 else \
                                 "**" if result['overall_pvalue'] < 0.01 else \
                                 "*" if result['overall_pvalue'] < 0.05 else "ns"
                    
                    report_lines.append(
                        f"| {layer_name} | {result['sample_size']} | "
                        f"{result['explained_variance']:.2f} | "
                        f"{result['overall_pvalue']:.4f} | {significance} |"
                    )
        else:
            # 整体分析结果
            if rda_results is not None:
                significance = "***" if rda_results['overall_pvalue'] < 0.001 else \
                             "**" if rda_results['overall_pvalue'] < 0.01 else \
                             "*" if rda_results['overall_pvalue'] < 0.05 else "ns"
                
                report_lines.extend([
                    "## 整体分析结果",
                    "",
                    f"- **样本数量**: {rda_results['sample_size']}",
                    f"- **总解释度**: {rda_results['explained_variance']:.2f}%",
                    f"- **显著性**: P = {rda_results['overall_pvalue']:.4f} ({significance})",
                    f"- **约束轴数**: {len(rda_results['constrained_eigenvals'])}",
                    f"- **非约束轴数**: {len(rda_results['unconstrained_eigenvals'])}",
                    ""
                ])
                
                # 添加轴解释度信息
                if rda_results['axis_variance']:
                    report_lines.extend([
                        "### 各轴解释度",
                        ""
                    ])
                    for axis_name, variance in rda_results['axis_variance'].items():
                        report_lines.append(f"- **{axis_name}**: {variance:.2f}%")
                    report_lines.append("")
        
        # 添加数据概况
        report_lines.extend([
            "## 数据概况",
            "",
            f"- **响应变量**: {', '.join(processed_data['response_vars'])}",
            f"- **环境变量数量**: {len(processed_data.get('env_vars', []))}",
            f"- **数据维度**: {len(processed_data['data'])}行 × {len(processed_data['data'].columns)}列",
            ""
        ])
        
        # 保存Markdown报告
        report_file = output_dir / "RDA分析摘要报告.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
            
        self.log(f"✅ 摘要报告已保存: {report_file.name}")