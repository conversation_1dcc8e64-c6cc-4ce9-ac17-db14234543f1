# RDA冗余分析项目 (Python版本)

## 项目简介

本项目是基于原R语言RDA冗余分析项目的Python重构版本，专门用于东北黑土区土壤剖面的冗余分析。项目保持了原有的统计分析逻辑和功能完整性，但使用Python的数据科学生态系统重新实现，提供了更好的可视化效果和更高的执行效率。

## 主要功能

- **数据预处理模块**: 清洗和准备用于RDA分析的环境和物种数据
- **RDA统计分析**: 执行冗余分析，计算排序轴、特征值和显著性检验
- **高质量可视化**: 生成发表级的RDA排序图、物种-环境关系图和统计图表
- **结果导出系统**: 输出分析结果、统计表格和高分辨率图像文件
- **VIF分析集成**: 整合多重共线性检测功能

## 技术栈

- **Python**: 3.8+
- **数据处理**: pandas, numpy
- **统计分析**: scikit-learn, scipy, statsmodels
- **RDA专用**: scikit-bio (用于生态学排序分析)
- **可视化**: matplotlib, seaborn, plotly
- **数据导入导出**: openpyxl, xlsxwriter

## 项目结构

```
07 RDA冗余分析_Python/
├── main.py                    # 主程序入口
├── config.py                  # 配置文件
├── requirements.txt           # 依赖包列表
├── README.md                  # 项目说明文档
├── src/                       # 源代码目录
│   ├── data_processing.py     # 数据处理模块
│   ├── rda_analysis.py       # RDA分析核心模块
│   ├── visualization.py      # 高质量可视化模块
│   └── export_results.py     # 结果导出模块
├── data/                      # 数据目录
├── outputs/                   # 输出结果目录
│   ├── plots/                # 图表输出
│   ├── tables/               # 表格输出
│   └── reports/              # 分析报告
```

## 安装和使用

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv rda_env

# 激活虚拟环境
# Windows:
rda_env\Scripts\activate
# Linux/Mac:
source rda_env/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

编辑 `config.py` 文件，设置数据文件路径和分析参数：

```python
# 数据文件路径
VIF_FILE_PATH = "path/to/your/vif_data.xlsx"
CHANGE_FILE_PATH = "path/to/your/change_data.xlsx"

# 分析配置
USE_STRATIFIED_RDA = False  # 是否进行分层RDA
USE_COUNTY_COVARIATE = False  # 是否使用县域协变量
PERMUTATION_TESTS = 999  # 置换检验次数
```

### 3. 运行分析

```bash
python main.py
```

## 输出结果

### 图表文件 (outputs/plots/)
- `RDA双序图_整体.png`: 主要的RDA排序图
- `环境变量重要性分析.png`: 变量重要性条形图
- `RDA总体解释度.png`: 解释度饼图
- `{响应变量}_驱动因子分析.png`: 各响应变量的驱动因子分析
- `{响应变量}_变量重要性.png`: 各响应变量的重要性分析

### 数据表格 (outputs/tables/)
- 样本得分、变量得分等数据表格

### 分析报告 (outputs/reports/)
- `RDA分析结果.xlsx`: 详细的Excel分析报告
- `RDA分析摘要.txt`: 文本格式的分析摘要
- `RDA分析摘要报告.md`: Markdown格式的摘要报告

## 核心模块说明

### 1. 数据处理模块 (data_processing.py)
- 数据加载和验证
- 缺失值处理
- 转移模式分析
- 变量分类和预处理

### 2. RDA分析模块 (rda_analysis.py)
- 冗余分析核心算法
- 置换检验
- 变量重要性计算
- 分层和整体分析

### 3. 可视化模块 (visualization.py)
- RDA双序图绘制
- 变量重要性图表
- 解释度对比图
- 分响应变量分析图

### 4. 结果导出模块 (export_results.py)
- Excel报告生成
- 文本摘要导出
- Markdown报告创建

## 配置参数说明

### 文件路径配置
```python
VIF_FILE_PATH = "VIF筛选结果文件路径"
CHANGE_FILE_PATH = "变化量数据文件路径"
OUTPUT_DIR = "输出目录路径"
```

### 分析参数配置
```python
USE_COUNTY_COVARIATE = False    # 是否使用县域协变量
USE_STRATIFIED_RDA = False      # 是否进行分层RDA分析
MISSING_THRESHOLD = 0.3         # 缺失值阈值
PERMUTATION_TESTS = 999         # 置换检验次数
```

### 响应变量定义
```python
RESPONSE_VARS = ["△pH", "△SOM", "△TN", "△TP", "△物理粘粒"]
```

### 深度分层定义
```python
DEPTH_LAYERS = {
    "0-10cm": [5],      # 深度中点5对应0-10cm
    "10-30cm": [20],    # 深度中点20对应10-30cm
    "30-60cm": [45],    # 深度中点45对应30-60cm
    "60-100cm": [80]    # 深度中点80对应60-100cm
}
```

## 与R版本的对应关系

| R文件 | Python模块 | 主要功能 |
|-------|------------|----------|
| 01_RDA_main.R | main.py | 主程序流程控制 |
| 02_load_packages.R | requirements.txt + imports | 依赖管理 |
| 03_data_processing.R | data_processing.py | 数据预处理和清洗 |
| 04_rda_analysis.R | rda_analysis.py | RDA核心分析算法 |
| 05_publication_plots.R | visualization.py | 高质量科研图表 |
| 06_export_results.R | export_results.py | 结果导出和报告生成 |
| vif_analysis.py | 集成到项目框架中 | VIF分析功能 |

## 算法说明

### RDA算法实现
本项目使用Python科学计算库实现RDA算法：
1. **多元线性回归**: 使用最小二乘法计算回归系数
2. **约束排序**: 对拟合值进行主成分分析
3. **非约束排序**: 对残差进行主成分分析
4. **置换检验**: 通过随机置换响应变量进行显著性检验

### 变量重要性计算
- 计算环境变量与排序轴的相关系数
- 使用R²值作为重要性指标
- 通过置换检验评估显著性

## 注意事项

1. **数据格式**: 确保输入数据格式与原R项目一致
2. **中文支持**: 项目支持中文变量名和图表标签
3. **内存使用**: 大数据集可能需要调整内存设置
4. **依赖版本**: 建议使用指定版本的依赖包以确保兼容性

## 故障排除

### 常见问题
1. **字体问题**: 如果中文显示异常，请安装SimHei字体
2. **内存不足**: 可以减少置换检验次数或使用分层分析
3. **数据路径**: 确保配置文件中的路径正确

### 错误处理
项目包含完整的错误处理机制，会在控制台输出详细的错误信息和建议。

## 更新日志

### v1.0.0 (2024-01-31)
- 完成R语言项目的Python迁移
- 实现所有核心功能模块
- 添加高质量可视化功能
- 完善文档和使用说明

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。请确保：
1. 代码符合PEP 8规范
2. 添加适当的注释和文档
3. 测试新功能的正确性

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues: 在GitHub上提交Issue
- 邮箱: [您的邮箱]

---

**注意**: 本项目是学术研究工具，请在使用时遵循相关的学术规范和引用要求。