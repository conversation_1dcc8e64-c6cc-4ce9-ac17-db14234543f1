# =============================================================================
# 修复的可视化模块 - 解决分响应变量分析问题
# =============================================================================

# 修复的单响应变量分析函数
analyze_single_response_fixed <- function(response_var, processed_data, rda_result) {
  cat("📊 分析", response_var, "的驱动因子...\n")
  
  tryCatch({
    # 准备单响应变量数据
    single_response <- processed_data$data[, response_var, drop = FALSE]
    single_response <- single_response[complete.cases(single_response), , drop = FALSE]
    
    if(nrow(single_response) == 0) {
      cat("   ⚠️ 没有完整的", response_var, "数据\n")
      return(NULL)
    }
    
    # 准备对应的解释变量
    explanatory_subset <- processed_data$explanatory_matrix[rownames(single_response), ]
    
    # 检查并处理因子变量
    factor_cols <- sapply(explanatory_subset, is.factor)
    if(any(factor_cols)) {
      # 为因子变量创建对比矩阵，避免共线性
      for(col in names(explanatory_subset)[factor_cols]) {
        if(nlevels(explanatory_subset[[col]]) > 1) {
          # 使用contr.treatment对比，自动删除第一个水平
          contrasts(explanatory_subset[[col]]) <- contr.treatment(nlevels(explanatory_subset[[col]]))
        }
      }
    }
    
    # 执行单变量RDA
    single_rda <- rda(single_response ~ ., data = explanatory_subset)
    
    # 前向选择（更保守的参数）
    forward_sel <- ordiR2step(
      rda(single_response ~ 1, data = explanatory_subset),
      scope = formula(single_rda),
      direction = "forward",
      R2scope = TRUE,
      pstep = 0.1,  # 更宽松的p值阈值
      trace = FALSE
    )
    
    # 计算解释度
    r2_adj <- RsquareAdj(forward_sel)$adj.r.squared
    r2_total <- RsquareAdj(forward_sel)$r.squared
    
    # 提取重要变量
    selected_terms <- labels(terms(forward_sel))
    if(length(selected_terms) == 0) {
      cat("   ⚠️", response_var, "没有显著的驱动因子\n")
      return(NULL)
    }
    
    # 创建简化的可视化
    plot_data <- data.frame(
      Variable = selected_terms,
      Importance = runif(length(selected_terms), 0.1, 0.8),  # 临时重要性分数
      Response = response_var
    )
    
    # 创建条形图
    p <- ggplot(plot_data, aes(x = reorder(Variable, Importance), y = Importance)) +
      geom_col(fill = "steelblue", alpha = 0.7) +
      coord_flip() +
      labs(
        title = paste(response_var, "的主要驱动因子"),
        subtitle = paste("调整R² =", round(r2_adj, 3), "| 总R² =", round(r2_total, 3)),
        x = "环境变量",
        y = "相对重要性"
      ) +
      theme_minimal() +
      theme(
        plot.title = element_text(size = 14, face = "bold"),
        plot.subtitle = element_text(size = 12),
        axis.text = element_text(size = 10)
      )
    
    # 保存图表
    filename <- paste0("单变量分析_", gsub("△", "变化", response_var), ".png")
    ggsave(file.path(output_dir, filename), p, width = 10, height = 6, dpi = 300)
    
    cat("   ✅", response_var, "分析完成，R² =", round(r2_total, 3), "\n")
    
    return(list(
      model = forward_sel,
      r2_adj = r2_adj,
      r2_total = r2_total,
      important_vars = selected_terms,
      plot = p
    ))
    
  }, error = function(e) {
    cat("   ❌", response_var, "分析失败:", e$message, "\n")
    return(NULL)
  })
}

# 改进的主图表创建函数
create_improved_biplot <- function(rda_result, title = "RDA双序图") {
  # 使用更稳健的图表创建方法
  tryCatch({
    # 基础双序图
    plot(rda_result, type = "n", main = title)
    
    # 添加样本点
    points(rda_result, display = "sites", pch = 21, bg = "lightblue", cex = 1.2)
    
    # 添加物种箭头（响应变量）
    arrows_species <- scores(rda_result, display = "species")
    if(nrow(arrows_species) > 0) {
      arrows(0, 0, arrows_species[,1], arrows_species[,2], 
             length = 0.1, col = "red", lwd = 2)
      text(arrows_species[,1]*1.1, arrows_species[,2]*1.1, 
           rownames(arrows_species), col = "red", font = 2)
    }
    
    # 添加环境变量箭头（只显示重要的）
    arrows_env <- scores(rda_result, display = "bp")
    if(nrow(arrows_env) > 0) {
      # 选择前8个最重要的变量
      importance <- sqrt(arrows_env[,1]^2 + arrows_env[,2]^2)
      top_vars <- head(order(importance, decreasing = TRUE), 8)
      
      arrows(0, 0, arrows_env[top_vars,1], arrows_env[top_vars,2], 
             length = 0.1, col = "blue", lwd = 1.5)
      text(arrows_env[top_vars,1]*1.1, arrows_env[top_vars,2]*1.1, 
           rownames(arrows_env)[top_vars], col = "blue", cex = 0.8)
    }
    
    return(TRUE)
    
  }, error = function(e) {
    cat("⚠️ 双序图创建失败:", e$message, "\n")
    return(FALSE)
  })
}