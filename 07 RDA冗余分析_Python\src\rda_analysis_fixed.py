# -*- coding: utf-8 -*-
"""
RDA分析核心模块（修复版）
基于原R语言项目的RDA分析逻辑，使用Python科学计算库实现
"""

import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from scipy import stats
from scipy.spatial.distance import pdist, squareform
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Union

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))
from config import *

class RDAAnalyzer:
    """RDA分析类"""
    
    def __init__(self):
        self.logger_prefix = "📈 [RDA分析]"
        
    def log(self, message: str):
        """日志输出"""
        print(f"{self.logger_prefix} {message}")
        
    def perform_rda_analysis(self, response_data: pd.DataFrame, 
                           explanatory_data: pd.DataFrame,
                           condition_data: Optional[pd.DataFrame] = None,
                           layer_name: str = "全部数据",
                           permutations: int = PERMUTATION_TESTS) -> Dict[str, Any]:
        """RDA分析函数"""
        self.log(f"\n📊 执行RDA分析: {layer_name}")
        
        # 检查数据完整性
        if len(response_data) != len(explanatory_data):
            raise ValueError("响应变量和解释变量的行数不匹配")
            
        # 移除常量列
        constant_cols = []
        for col in explanatory_data.columns:
            if explanatory_data[col].nunique() <= 1:
                constant_cols.append(col)
                
        if constant_cols:
            self.log(f"🗑️ 移除常量列: {', '.join(constant_cols)}")
            explanatory_data = explanatory_data.drop(columns=constant_cols)
            
        if explanatory_data.empty:
            self.log("⚠️ 没有有效的解释变量")
            return None
            
        # 数据标准化
        response_scaled = StandardScaler().fit_transform(response_data)
        explanatory_scaled = StandardScaler().fit_transform(explanatory_data)
        
        # 执行RDA（使用CCA近似实现）
        self.log("🔍 执行冗余分析...")
        
        # 计算响应变量的协方差矩阵
        Y = response_scaled
        X = explanatory_scaled
        
        # 计算RDA
        # Y = X * B + E (其中B是回归系数矩阵)
        # RDA等价于对拟合值进行PCA
        
        # 多元线性回归
        try:
            # 使用最小二乘法求解回归系数
            B = np.linalg.lstsq(X, Y, rcond=None)[0]  # X.T @ X^(-1) @ X.T @ Y
            Y_fitted = X @ B  # 拟合值
            Y_residual = Y - Y_fitted  # 残差
            
            # 对拟合值进行PCA（约束排序）
            pca_fitted = PCA()
            constrained_scores = pca_fitted.fit_transform(Y_fitted)
            
            # 对残差进行PCA（非约束排序）
            pca_residual = PCA()
            unconstrained_scores = pca_residual.fit_transform(Y_residual)
            
            # 计算特征值
            constrained_eigenvals = pca_fitted.explained_variance_
            unconstrained_eigenvals = pca_residual.explained_variance_
            
            # 计算解释度
            total_var = np.sum(constrained_eigenvals) + np.sum(unconstrained_eigenvals)
            constrained_var = np.sum(constrained_eigenvals)
            explained_variance = (constrained_var / total_var * 100) if total_var > 0 else 0
            
            self.log(f"📈 总解释度: {explained_variance:.2f}%")
            self.log(f"📊 约束轴数: {len(constrained_eigenvals)}")
            self.log(f"📊 非约束轴数: {len(unconstrained_eigenvals)}")
            
            # 计算各轴解释度
            if len(constrained_eigenvals) > 0:
                axis_variance = constrained_eigenvals / np.sum(constrained_eigenvals) * explained_variance
                axis_names = [f"RDA{i+1}" for i in range(len(axis_variance))]
                axis_variance_dict = dict(zip(axis_names, axis_variance))
            else:
                axis_variance_dict = {}
                
            # 进行置换检验
            self.log("🔬 进行显著性检验...")
            overall_pvalue = self._permutation_test(Y, X, permutations)
            
            # 构建结果
            result = {
                'constrained_scores': constrained_scores,
                'unconstrained_scores': unconstrained_scores,
                'constrained_eigenvals': constrained_eigenvals,
                'unconstrained_eigenvals': unconstrained_eigenvals,
                'explained_variance': explained_variance,
                'axis_variance': axis_variance_dict,
                'layer_name': layer_name,
                'sample_size': len(response_data),
                'variable_count': len(explanatory_data.columns),
                'explanatory_data': explanatory_data,
                'response_data': response_data,
                'overall_pvalue': overall_pvalue,
                'regression_coefficients': B,
                'fitted_values': Y_fitted,
                'residuals': Y_residual
            }
            
            return result
            
        except Exception as e:
            self.log(f"❌ RDA分析失败: {e}")
            return None
            
    def _permutation_test(self, Y: np.ndarray, X: np.ndarray, 
                         permutations: int = 999) -> float:
        """置换检验"""
        # 计算原始F统计量
        n, p = X.shape
        m = Y.shape[1]
        
        # 回归分析
        B = np.linalg.lstsq(X, Y, rcond=None)[0]
        Y_fitted = X @ B
        Y_residual = Y - Y_fitted
        
        # 计算F统计量
        SSR = np.sum(Y_fitted ** 2)  # 回归平方和
        SSE = np.sum(Y_residual ** 2)  # 残差平方和
        
        df_reg = min(p, m)  # 回归自由度
        df_res = n - p - 1  # 残差自由度
        
        if df_res <= 0:
            return 1.0
            
        MSR = SSR / df_reg
        MSE = SSE / df_res
        F_obs = MSR / MSE if MSE > 0 else 0
        
        # 置换检验
        F_perm = []
        for _ in range(permutations):
            # 随机置换响应变量
            Y_perm = Y[np.random.permutation(n)]
            
            # 重新计算F统计量
            try:
                B_perm = np.linalg.lstsq(X, Y_perm, rcond=None)[0]
                Y_fitted_perm = X @ B_perm
                Y_residual_perm = Y_perm - Y_fitted_perm
                
                SSR_perm = np.sum(Y_fitted_perm ** 2)
                SSE_perm = np.sum(Y_residual_perm ** 2)
                
                MSR_perm = SSR_perm / df_reg
                MSE_perm = SSE_perm / df_res
                F_perm_val = MSR_perm / MSE_perm if MSE_perm > 0 else 0
                F_perm.append(F_perm_val)
            except:
                F_perm.append(0)
                
        # 计算p值
        F_perm = np.array(F_perm)
        p_value = np.sum(F_perm >= F_obs) / len(F_perm)
        
        return p_value
        
    def perform_overall_rda(self, processed_data: Dict[str, Any], 
                          use_county: bool = USE_COUNTY_COVARIATE) -> Dict[str, Any]:
        """整体RDA分析函数"""
        self.log("\n=== 整体RDA分析 ===")
        
        # 准备响应变量矩阵
        response_matrix = processed_data['data'][processed_data['response_vars']].dropna()
        self.log(f"📊 整体分析样本数: {len(response_matrix)}")
        
        # 准备解释变量
        explanatory_matrix = processed_data['explanatory_matrix'].loc[response_matrix.index].copy()
        
        # 处理分类变量：转换为虚拟变量
        categorical_cols = explanatory_matrix.select_dtypes(exclude=[np.number]).columns
        if len(categorical_cols) > 0:
            self.log(f"🔄 处理分类变量: {', '.join(categorical_cols)}")
            
            # 为每个分类变量创建虚拟变量
            dummy_dfs = []
            numeric_df = explanatory_matrix.select_dtypes(include=[np.number])
            
            for col in categorical_cols:
                # 创建虚拟变量，删除第一个类别以避免多重共线性
                dummies = pd.get_dummies(explanatory_matrix[col], prefix=col, drop_first=True)
                dummy_dfs.append(dummies)
                self.log(f"   {col}: {explanatory_matrix[col].nunique()}类别 → {dummies.shape[1]}个虚拟变量")
            
            # 合并所有变量
            if dummy_dfs:
                all_dummies = pd.concat(dummy_dfs, axis=1)
                explanatory_matrix = pd.concat([numeric_df, all_dummies], axis=1)
            else:
                explanatory_matrix = numeric_df
                
            self.log(f"   最终变量矩阵: {explanatory_matrix.shape[1]}个变量")
        
        # 处理数值型变量的缺失值
        numeric_cols = explanatory_matrix.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if explanatory_matrix[col].isnull().any():
                mean_val = explanatory_matrix[col].mean()
                explanatory_matrix[col] = explanatory_matrix[col].fillna(mean_val)
                
        # 准备条件变量（县域）
        condition_vars = None
        if use_county and COUNTY_COLUMN_NAME in processed_data['data'].columns:
            county_data = processed_data['data'].loc[response_matrix.index, COUNTY_COLUMN_NAME]
            unique_counties = county_data.unique()
            
            if len(unique_counties) > 1:
                condition_vars = pd.get_dummies(county_data, prefix='County')
                self.log(f"🏘️ 使用县域作为条件变量，县数: {len(unique_counties)}")
            else:
                self.log("🏘️ 只有一个县，跳过县域条件变量")
                
        # 执行RDA分析
        rda_result = self.perform_rda_analysis(
            response_data=response_matrix,
            explanatory_data=explanatory_matrix,
            condition_data=condition_vars,
            layer_name="整体数据",
            permutations=PERMUTATION_TESTS
        )
        
        return rda_result
        
    def calculate_variable_importance(self, rda_result: Dict[str, Any]) -> pd.DataFrame:
        """计算变量重要性"""
        if rda_result is None:
            return pd.DataFrame()
            
        explanatory_data = rda_result['explanatory_data']
        response_data = rda_result['response_data']
        
        # 计算每个变量与排序轴的相关性
        constrained_scores = rda_result['constrained_scores']
        
        if constrained_scores.shape[1] == 0:
            return pd.DataFrame()
            
        # 计算变量与第一排序轴的相关性作为重要性指标
        importance_data = []
        
        for i, var_name in enumerate(explanatory_data.columns):
            var_values = explanatory_data.iloc[:, i]
            
            # 计算与第一轴的相关性
            if constrained_scores.shape[1] > 0:
                corr_axis1 = np.corrcoef(var_values, constrained_scores[:, 0])[0, 1]
                r_squared = corr_axis1 ** 2
            else:
                r_squared = 0
                corr_axis1 = 0
                
            # 简化的显著性检验（基于相关系数）
            n = len(var_values)
            if n > 2 and not np.isnan(corr_axis1):
                t_stat = corr_axis1 * np.sqrt((n - 2) / (1 - corr_axis1**2 + 1e-10))
                p_value = 2 * (1 - stats.t.cdf(abs(t_stat), n - 2))
            else:
                p_value = 1.0
                
            importance_data.append({
                'Variable': var_name,
                'R_squared': r_squared,
                'Correlation': corr_axis1,
                'P_value': p_value,
                'Significant': p_value < 0.05
            })
            
        importance_df = pd.DataFrame(importance_data)
        importance_df = importance_df.sort_values('R_squared', ascending=False)
        
        return importance_df