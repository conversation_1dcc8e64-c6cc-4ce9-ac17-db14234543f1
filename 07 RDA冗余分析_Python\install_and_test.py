# -*- coding: utf-8 -*-
"""
安装和测试脚本
用于验证Python环境和依赖包的安装情况
"""

import sys
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"   当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("   ✅ Python版本符合要求 (>=3.8)")
        return True
    else:
        print("   ❌ Python版本过低，需要3.8或更高版本")
        return False

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("   ❌ requirements.txt文件不存在")
        return False
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("   ✅ pip已升级到最新版本")
        
        # 安装依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)])
        print("   ✅ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"   ❌ 依赖包安装失败: {e}")
        return False

def test_imports():
    """测试关键包的导入"""
    print("\n🔍 测试关键包导入...")
    
    packages = [
        ("pandas", "数据处理"),
        ("numpy", "数值计算"),
        ("matplotlib", "基础绘图"),
        ("seaborn", "统计绘图"),
        ("sklearn", "机器学习"),
        ("scipy", "科学计算"),
        ("openpyxl", "Excel处理")
    ]
    
    failed_packages = []
    
    for package, description in packages:
        try:
            __import__(package)
            print(f"   ✅ {package} ({description}) - 导入成功")
        except ImportError as e:
            print(f"   ❌ {package} ({description}) - 导入失败: {e}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️ 以下包导入失败: {', '.join(failed_packages)}")
        print("请尝试手动安装: pip install " + " ".join(failed_packages))
        return False
    else:
        print("\n✅ 所有关键包导入成功")
        return True

def test_project_structure():
    """测试项目结构"""
    print("\n📁 检查项目结构...")
    
    project_root = Path(__file__).parent
    required_files = [
        "main.py",
        "config.py",
        "requirements.txt",
        "README.md",
        "src/data_processing.py",
        "src/rda_analysis.py",
        "src/visualization.py",
        "src/export_results.py"
    ]
    
    required_dirs = [
        "src",
        "data",
        "outputs",
        "outputs/plots",
        "outputs/tables",
        "outputs/reports"
    ]
    
    missing_files = []
    missing_dirs = []
    
    # 检查文件
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - 文件缺失")
            missing_files.append(file_path)
    
    # 检查目录
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        if full_path.exists() and full_path.is_dir():
            print(f"   ✅ {dir_path}/")
        else:
            print(f"   ❌ {dir_path}/ - 目录缺失")
            missing_dirs.append(dir_path)
    
    if missing_files or missing_dirs:
        print(f"\n⚠️ 项目结构不完整")
        if missing_files:
            print(f"缺失文件: {', '.join(missing_files)}")
        if missing_dirs:
            print(f"缺失目录: {', '.join(missing_dirs)}")
        return False
    else:
        print("\n✅ 项目结构完整")
        return True

def test_config():
    """测试配置文件"""
    print("\n⚙️ 检查配置文件...")
    
    try:
        # 尝试导入配置
        sys.path.append(str(Path(__file__).parent))
        import config
        
        # 检查关键配置项
        required_configs = [
            'VIF_FILE_PATH',
            'CHANGE_FILE_PATH',
            'OUTPUT_DIR',
            'RESPONSE_VARS',
            'VARIABLE_GROUPS'
        ]
        
        missing_configs = []
        for config_name in required_configs:
            if hasattr(config, config_name):
                print(f"   ✅ {config_name}")
            else:
                print(f"   ❌ {config_name} - 配置缺失")
                missing_configs.append(config_name)
        
        if missing_configs:
            print(f"\n⚠️ 配置文件不完整，缺失: {', '.join(missing_configs)}")
            return False
        else:
            print("\n✅ 配置文件完整")
            return True
            
    except Exception as e:
        print(f"   ❌ 配置文件导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 === RDA冗余分析项目安装和测试 ===\n")
    
    tests = [
        ("Python版本检查", check_python_version),
        ("依赖包安装", install_dependencies),
        ("包导入测试", test_imports),
        ("项目结构检查", test_project_structure),
        ("配置文件检查", test_config)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"执行: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*50}")
    print("测试结果总结")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！项目已准备就绪。")
        print("现在可以运行: python main.py")
    else:
        print(f"\n⚠️ 有 {total - passed} 项测试失败，请检查并修复问题。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)