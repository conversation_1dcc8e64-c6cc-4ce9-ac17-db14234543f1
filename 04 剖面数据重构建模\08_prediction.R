# =============================================================================
# 预测和缺失值填补模块
#
# 功能：
# 1. 预测和填补缺失值主函数
# 2. 各方法的预测函数
# 3. 数据更新函数
# 4. 保底策略函数
# =============================================================================



# --- 随机森林预测函数 ---
predict_with_random_forest <- function(complete_data, model_result, config) {
  tryCatch({
    if(is.null(model_result$model)) {
      cat("随机森林模型不存在\n")
      return(NULL)
    }

    # 获取参数
    soil_property <- model_result$soil_property

    # 自动分离预测数据（只预测目标变量为NA的样本）
    prediction_data <- complete_data[is.na(complete_data[[soil_property]]), ]

    # 检查预测数据
    if(nrow(prediction_data) == 0) {
      cat("没有需要预测的样本\n")
      return(numeric(0))
    }

    cat("随机森林预测 - 样本数:", nrow(prediction_data), "\n")

    # 获取建模时的特征列表
    feature_cols <- model_result$features
    cat("使用特征:", length(feature_cols), "个\n")

    # 提取预测特征（数据已经标准化和处理过缺失值）
    predict_data <- prediction_data[, feature_cols, drop = FALSE]

    # 进行预测
    predictions <- predict(model_result$model, predict_data)

    return(as.numeric(predictions))

  }, error = function(e) {
    cat("随机森林预测失败:", e$message, "\n")
    return(NULL)
  })
}

# --- 普通克里金预测函数 ---
predict_with_ordinary_kriging <- function(modeling_data, prediction_data, model_result, config) {
  tryCatch({
    # 获取参数
    tuned_params <- model_result$tuned_params
    soil_property <- model_result$soil_property

    # 检查数据
    if(nrow(prediction_data) == 0) {
      cat("没有需要预测的样本\n")
      return(numeric(0))
    }

    cat("普通克里金预测 - 建模样本:", nrow(modeling_data), "个，预测样本:", nrow(prediction_data), "个\n")

    # 用所有可用数据建立最终的普通克里金模型并预测
    krige_result <- tryCatch({
      # 设置坐标系统
      coordinates(modeling_data) <- ~ proj_x + proj_y
      coordinates(prediction_data) <- ~ proj_x + proj_y

      # 构建公式
      formula_obj <- as.formula(paste(soil_property, "~ 1"))

      # 计算经验变异函数
      emp_vgm <- variogram(formula_obj, modeling_data)

      # 拟合变异函数模型
      fitted_vgm <- fit.variogram(emp_vgm, vgm(tuned_params$vgm_model))

      if(is.null(fitted_vgm) || any(is.na(fitted_vgm$psill))) {
        return(NULL)
      }

      # 计算最大搜索距离
      if(!is.null(tuned_params$maxdist_factor)) {
        coords <- coordinates(modeling_data)
        x_range <- diff(range(coords[,1]))
        y_range <- diff(range(coords[,2]))
        maxdist <- tuned_params$maxdist_factor * sqrt(x_range^2 + y_range^2)
      } else {
        maxdist <- Inf
      }

      # 进行克里金预测
      krige_pred <- krige(formula_obj, modeling_data, prediction_data, model = fitted_vgm,
                         nmax = tuned_params$nmax, nmin = tuned_params$nmin, maxdist = maxdist)

      as.numeric(krige_pred$var1.pred)

    }, error = function(e) {
      cat("普通克里金建模预测失败:", e$message, "\n")
      NULL
    })

    if(is.null(krige_result)) {
      cat("普通克里金预测失败：可能原因包括变异函数拟合失败、样本数量不足或空间分布不当\n")
      return(NULL)
    }

    # 提取预测值（krige_result已经是数值向量）
    predictions <- krige_result

    # 检查预测结果
    valid_predictions <- predictions[!is.na(predictions)]
    if(length(valid_predictions) == 0) {
      cat("普通克里金预测失败：所有预测值为NA\n")
      return(NULL)
    }

    if(length(valid_predictions) < length(predictions)) {
      cat("警告：", length(predictions) - length(valid_predictions), "个预测值为NA\n")
    }

    cat("普通克里金预测完成:", length(predictions[!is.na(predictions)]), "个有效预测值\n")

    return(as.numeric(predictions))

  }, error = function(e) {
    cat("普通克里金预测失败:", e$message, "\n")
    return(NULL)
  })
}

# --- 回归克里金预测函数 ---
predict_with_regression_kriging <- function(modeling_data, prediction_data, model_result, config) {
  tryCatch({
    # 获取建模时使用的协变量
    env_variables <- model_result$env_variables
    if(is.null(env_variables) || length(env_variables) == 0) {
      cat("回归克里金协变量信息不存在\n")
      return(NULL)
    }

    cat("回归克里金协变量:", length(env_variables), "个:", paste(env_variables, collapse = ", "), "\n")

    # 检查数据中是否包含所需的协变量
    missing_vars <- setdiff(env_variables, names(modeling_data))
    if(length(missing_vars) > 0) {
      cat("建模数据中缺少协变量:", paste(missing_vars, collapse = ", "), "\n")
      return(NULL)
    }

    missing_vars_pred <- setdiff(env_variables, names(prediction_data))
    if(length(missing_vars_pred) > 0) {
      cat("预测数据中缺少协变量:", paste(missing_vars_pred, collapse = ", "), "\n")
      return(NULL)
    }

    # 获取参数
    tuned_params <- model_result$tuned_params
    soil_property <- model_result$soil_property

    # 检查数据
    if(nrow(prediction_data) == 0) {
      cat("没有需要预测的样本\n")
      return(numeric(0))
    }

    cat("回归克里金预测 - 建模样本:", nrow(modeling_data), "个，预测样本:", nrow(prediction_data), "个\n")

    # 用所有可用数据建立最终的回归克里金模型并预测
    krige_result <- tryCatch({
      # 设置坐标系统
      coordinates(modeling_data) <- ~ proj_x + proj_y
      coordinates(prediction_data) <- ~ proj_x + proj_y

      # 第一步：拟合回归模型
      formula_obj <- as.formula(paste(soil_property, "~", paste(env_variables, collapse = " + ")))
      modeling_df <- as.data.frame(modeling_data)
      lm_model <- lm(formula_obj, data = modeling_df)

      # 计算残差
      modeling_data$residuals <- residuals(lm_model)

      # 第二步：对残差拟合变异函数
      emp_vgm <- variogram(residuals ~ 1, modeling_data)
      fitted_vgm <- fit.variogram(emp_vgm, vgm(tuned_params$vgm_model))

      if(is.null(fitted_vgm) || any(is.na(fitted_vgm$psill))) {
        return(NULL)
      }

      # 第三步：预测
      # 3.1 用回归模型预测趋势
      prediction_df <- as.data.frame(prediction_data)
      trend_pred <- predict(lm_model, newdata = prediction_df)

      # 3.2 对残差进行克里金预测
      residual_pred <- krige(residuals ~ 1, modeling_data, prediction_data, model = fitted_vgm,
                            nmax = tuned_params$nmax, nmin = tuned_params$nmin)

      # 3.3 组合预测结果：趋势 + 残差克里金
      final_pred <- trend_pred + residual_pred$var1.pred

      as.numeric(final_pred)

    }, error = function(e) {
      cat("回归克里金建模预测失败:", e$message, "\n")
      NULL
    })

    if(is.null(krige_result)) {
      cat("回归克里金预测失败：可能原因包括变异函数拟合失败、协方差矩阵奇异或空间相关性不足\n")
      return(NULL)
    }

    # 提取预测值
    predictions <- krige_result

    # 检查预测结果
    valid_predictions <- predictions[!is.na(predictions)]
    if(length(valid_predictions) == 0) {
      cat("回归克里金预测失败：所有预测值为NA\n")
      return(NULL)
    }

    if(length(valid_predictions) < length(predictions)) {
      cat("警告：", length(predictions) - length(valid_predictions), "个预测值为NA\n")
    }

    cat("回归克里金预测完成:", length(predictions[!is.na(predictions)]), "个有效预测值\n")

    return(as.numeric(predictions))

  }, error = function(e) {
    cat("回归克里金预测失败:", e$message, "\n")
    return(NULL)
  })
}

# --- 更新填补后的数据 ---
update_filled_data <- function(original_data, filled_result, county, year, soil_property, layer_name, config) {



  if(is.null(filled_result) || is.null(filled_result$values)) {
    cat("没有填补值可更新\n")
    return(original_data)
  }

  # 获取深度层配置
  depth_layers <- config$general_options$depth_layers
  target_depth_config <- NULL
  for(depth_config in depth_layers) {
    if(depth_config$name == layer_name) {
      target_depth_config <- depth_config
      break
    }
  }

  if(is.null(target_depth_config)) {
    cat("未找到深度层配置:", layer_name, "\n")
    return(original_data)
  }

  # 筛选需要更新的数据
  depth_center <- target_depth_config$center
  update_mask <- original_data[[config$column_names$county]] == county &
                 original_data[[config$column_names$year]] == year &
                 original_data[[config$column_names$depth]] == depth_center &
                 is.na(original_data[[soil_property]])

  update_indices <- which(update_mask)

  if(length(update_indices) != length(filled_result$values)) {
    cat("警告：填补值数量(", length(filled_result$values), ")与缺失值数量(", length(update_indices), ")不匹配\n")
    # 取较小的数量
    n_update <- min(length(update_indices), length(filled_result$values))
    update_indices <- update_indices[1:n_update]
    filled_values <- filled_result$values[1:n_update]
  } else {
    filled_values <- filled_result$values
  }

  # 只更新有效的预测值（非NA且非负值）
  valid_mask <- !is.na(filled_values) & filled_values >= 0
  valid_indices <- update_indices[valid_mask]
  valid_values <- filled_values[valid_mask]

  if(length(valid_values) > 0) {
    # 更新数据（保留两位小数）
    original_data[valid_indices, soil_property] <- round(valid_values, 2)
    cat("成功更新", length(valid_values), "个有效预测值\n")
  } else {
    cat("没有有效的预测值可更新\n")
  }

  # 验证更新是否成功
  updated_missing <- sum(is.na(original_data[update_indices, soil_property]))


  return(original_data)
}



# --- 使用最佳方法进行预测 ---
predict_with_best_method <- function(modeling_results, data, county, year, soil_property, layer_name, config) {

  # 获取所有有效的建模结果
  all_results <- modeling_results$all_results
  valid_methods <- list()

  # 收集所有有效方法并按R²排序
  for(method_name in names(all_results)) {
    result <- all_results[[method_name]]
    if(!is.null(result) && !is.null(result$r_squared)) {
      valid_methods[[method_name]] <- result
    }
  }

  if(length(valid_methods) == 0) {
    cat("没有有效的建模方法可用于预测\n")
    return(NULL)
  }

  # 按R²降序排序
  r2_values <- sapply(valid_methods, function(x) x$r_squared)
  sorted_methods <- names(sort(r2_values, decreasing = TRUE))

  # 依次尝试每个方法
  for(i in seq_along(sorted_methods)) {
    method_name <- sorted_methods[i]
    method_result <- valid_methods[[method_name]]

    if(i == 1) {
      cat("使用最佳方法:", method_name, "（R² =", round(method_result$r_squared, 3), "）\n")
    } else {
      cat("尝试备选方法:", method_name, "（R² =", round(method_result$r_squared, 3), "）\n")
    }

    # 创建临时的modeling_results用于预测
    temp_modeling_results <- list(
      best_method = method_name,
      best_r2 = method_result$r_squared,
      best_rmse = method_result$rmse,
      best_result = method_result,
      all_results = modeling_results$all_results
    )

    # 准备预测数据
    layer_data <- extract_layer_data(data, county, year, layer_name, config)
    if(is.null(layer_data)) {
      cat("无法提取深度层数据\n")
      next
    }

    # 添加邻近层变量
    if(config$gap_filling$use_neighbor_layers) {
      all_layers_data <- extract_county_year_data(data, county, year, config)
      layer_data <- add_neighbor_layer_variables(layer_data, all_layers_data, layer_name, config, soil_property)
    }

    # 提取环境变量值（静默模式，避免重复输出）
    env_variables <- method_result$env_variables
    if(!is.null(env_variables) && length(env_variables) > 0) {
      layer_data <- extract_environmental_values(layer_data, env_variables, config, county, year, layer_name, silent = TRUE)
    }

    # 处理完整数据（包含建模+预测数据）
    data_prep_result <- prepare_data(
      layer_data = layer_data,  # 完整数据：有值的用于建模，NA的需要预测
      soil_property = soil_property,
      config = config,
      method_type = method_name,
      available_env_vars = env_variables,
      include_prediction_data = TRUE,
      silent = TRUE  # 预测时使用静默模式
    )

    if(is.null(data_prep_result)) {
      cat("数据准备失败\n")
      next
    }

    # 获取已经分离好的建模数据和预测数据
    modeling_data <- data_prep_result$modeling_data
    prediction_data <- data_prep_result$prediction_data

    if(is.null(prediction_data) || nrow(prediction_data) == 0) {
      cat("没有需要预测的数据\n")
      next
    }

    # 根据方法进行预测（传入分离好的数据）
    if(method_name == "regression_kriging") {
      filled_values <- predict_with_regression_kriging(modeling_data, prediction_data, method_result, config)
    } else if(method_name == "ordinary_kriging") {
      filled_values <- predict_with_ordinary_kriging(modeling_data, prediction_data, method_result, config)
    } else if(method_name == "random_forest") {
      filled_values <- predict_with_random_forest(prediction_data, method_result, config)
    } else {
      cat("未知的建模方法:", method_name, "\n")
      next
    }

    # 检查预测是否成功
    if(!is.null(filled_values) && length(filled_values) > 0) {
      # 统一反标准化（使用预测时计算的正确标准化参数）
      if(!is.null(data_prep_result$scaler_info) && soil_property %in% names(data_prep_result$scaler_info)) {
        cat("对预测结果进行反标准化...\n")
        target_scaler <- data_prep_result$scaler_info[[soil_property]]
        filled_values <- filled_values * target_scaler$sd + target_scaler$mean
        cat("反标准化完成，预测值范围:", round(min(filled_values, na.rm = TRUE), 3), "-", round(max(filled_values, na.rm = TRUE), 3), "\n")
      }

      # 检查预测值是否包含NA或负值
      na_count <- sum(is.na(filled_values))
      negative_count <- sum(filled_values < 0, na.rm = TRUE)
      invalid_count <- na_count + negative_count
      valid_count <- sum(!is.na(filled_values) & filled_values >= 0)

      # 输出检查结果
      if(na_count > 0 || negative_count > 0) {
        cat("部分成功（", valid_count, "个有效值")
        if(na_count > 0) cat("，", na_count, "个NA值")
        if(negative_count > 0) cat("，", negative_count, "个负值")
        cat("）\n")

        # 如果还有备选方法，保留当前有效值，继续尝试
        if(i < length(sorted_methods)) {
          if(na_count > 0 && negative_count > 0) {
            cat("保留有效预测值，尝试备选方法填补NA值和负值\n")
          } else if(na_count > 0) {
            cat("保留有效预测值，尝试备选方法填补NA值\n")
          } else {
            cat("保留有效预测值，尝试备选方法填补负值\n")
          }
          # 这里需要实现部分填补逻辑
          # 暂时继续尝试下一个方法
          next
        } else {
          cat("所有方法都已尝试，保留", valid_count, "个有效预测值\n")
        }
      } else {
        cat("预测完全成功！\n")
      }

      # 获取缺失值的索引
      missing_indices <- which(is.na(layer_data[[soil_property]]))

      # 返回预测结果
      return(list(
        values = filled_values,
        indices = missing_indices,
        method = method_name,
        na_count = na_count,
        negative_count = negative_count,
        invalid_count = invalid_count
      ))
    } else {
      cat("预测失败\n")
    }
  }

  cat("所有建模方法都已尝试完毕\n")
  return(NULL)
}


