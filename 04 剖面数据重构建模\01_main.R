# =============================================================================
# 缺失值填补主程序 - 统一流程
# 
# 固定流程：
# 1. 分析每个深度层的缺失情况
# 2. 只对有缺失值的深度层进行建模
# 3. 使用邻近深度层作为协变量
# 4. 三种方法自动调优对比
# 5. 选择最优方法填补缺失值
# 6. 输出填补后的Excel文件
# =============================================================================

# --- 设置工作环境 ---
cat("=== 缺失值填补系统启动 ===\n")

# 获取脚本所在目录
script_dir <- dirname(rstudioapi::getActiveDocumentContext()$path)
setwd(script_dir)

# 加载所有模块（按功能分组）
source("04_data.R")                # 数据加载和预处理（含通用工具函数）
source("05_modeling.R")            # 建模数据准备
source("06_tuning.R")              # 参数调优
source("07_evaluation.R")          # 交叉验证和评估
source("08_prediction.R")          # 预测和缺失值填补
source("09_report.R")              # 报告生成

# 加载建模方法模块
source("03_modeling_methods.R")

# 加载必要的包
load_required_packages()

# =============================================================================
# 主函数：缺失值填补分析
# =============================================================================
run_gap_filling_analysis <- function(config_file = "02_config.yml") {
  
  cat("\n=== 开始缺失值填补分析 ===\n")
  
  # 加载配置
  config <- load_config(config_file)
  
  # 测试模式设置
  if(config$experiment$test_mode) {
    counties <- config$experiment$test_counties
    years <- config$experiment$test_years
    soil_properties <- config$experiment$test_properties
    cat("运行在测试模式：处理", paste(counties, collapse=", "), "县",
        paste(years, collapse=", "), "年", paste(soil_properties, collapse=", "), "\n")
  } else {
    counties <- config$counties
    years <- config$years
    soil_properties <- config$target_soil_properties
    cat("运行在完整模式：处理", length(counties), "个县城,",
        length(years), "个年份,", length(soil_properties), "个土壤属性\n")
  }
  
  # 创建输出目录
  create_output_directories(config)
  
  # 加载完整数据（包含所有环境变量，用于缺失值分析、建模和预测）
  soil_data <- load_soil_data(config)

  if(is.null(soil_data) || nrow(soil_data) == 0) {
    stop("数据加载失败")
  }

  # 存储填补结果
  filled_data <- soil_data
  gap_filling_log <- list()

  # 根据配置决定处理模式
  if(config$combine_counties) {
    # 合并县城模式
    county_groups <- list("合并县城" = counties)
    cat("使用合并县城模式，样本量：", sum(sapply(counties, function(c) sum(soil_data$City == c))), "\n")
  } else {
    # 分县城模式
    county_groups <- as.list(setNames(counties, counties))
    cat("使用分县城模式\n")
  }

  # 批量处理
  total_tasks <- length(county_groups) * length(years) * length(soil_properties)
  current_task <- 0

  for(group_name in names(county_groups)) {
    group_counties <- county_groups[[group_name]]

    for(year in years) {
      for(soil_property in soil_properties) {

        current_task <- current_task + 1

        cat("处理 [", current_task, "/", total_tasks, "]:", group_name, year, "年", soil_property, "\n")
        cat(paste(rep("=", 60), collapse=""), "\n")

        # 步骤1：分析缺失情况（支持多县城）
        missing_analysis <- analyze_missing_by_depth_multi(
          filled_data, group_counties, year, soil_property, config
        )
        
        if(missing_analysis$total_missing == 0) {
          cat("该属性无缺失值，跳过建模\n")
          next
        }
        
        cat("发现", missing_analysis$total_missing, "个缺失值，需要建模", 
            length(missing_analysis$layers_with_missing), "个深度层\n")
        
        # 获取相关性数据（合并模式使用"典型县"路径）
        ref_county <- if(config$combine_counties) "典型县" else group_name
        correlation_data <- load_vif_results(config, ref_county, soil_property, year)

        # 检查是否有可用的相关性分析结果
        if(is.null(correlation_data)) {
          cat("无可用的相关性分析结果，将仅使用邻近深度层信息建模\n")
        } else {
          cat("相关性分析结果加载成功，将根据建模样本量自适应选择环境变量\n")
        }
        
        # 步骤2：对每个有缺失的深度层进行建模
        for(layer_name in names(missing_analysis$layers_with_missing)) {

          layer_info <- missing_analysis$layers_with_missing[[layer_name]]
          missing_count <- layer_info$missing_count

          cat("\n--- 建模深度层:", layer_name, "(缺失", missing_count, "个) ---\n")
          
          # 步骤3：准备建模数据（支持多县城）
          if(config$combine_counties) {
            # 合并县城模式：提取所有县城的数据
            layer_data <- extract_multi_layer_data(filled_data, group_counties, year, layer_name, config)
            all_layers_data <- extract_multi_county_data(filled_data, group_counties, year, config)
          } else {
            # 分县城模式：提取单个县城数据
            layer_data <- extract_layer_data(filled_data, group_name, year, layer_name, config)
            all_layers_data <- extract_county_year_data(filled_data, group_name, year, config)
          }

          if(is.null(layer_data)) {
            cat("无法提取深度层数据\n")
            next
          }

          # 添加邻近层变量
          if(config$gap_filling$use_neighbor_layers) {
            layer_data <- add_neighbor_layer_variables(layer_data, all_layers_data, layer_name, config, soil_property)
          }

          #根据样本量自适应选择环境变量 ===
          temp_data_prep <- prepare_data(layer_data, soil_property, config, "all", c(), include_prediction_data = FALSE, silent = TRUE)
          
          if(is.null(temp_data_prep)) {
            cat("无法获取建模样本量，跳过该深度层\n")
            next
          }
          
          actual_modeling_sample_size <- temp_data_prep$sample_size
          cat("实际建模样本量:", actual_modeling_sample_size, "个\n")
          
          # 根据实际建模样本量自适应选择环境变量
          selected_vars <- if(!is.null(correlation_data)) {
            if(is.list(correlation_data) && "correlation_data" %in% names(correlation_data)) {
              # 包含完整相关性数据
              select_vars_by_n(
                correlation_data$correlation_data, 
                actual_modeling_sample_size, 
                config
              )
            } else {
              cat("警告：处理环境变量出错，不使用环境变量\n")
              c()
            }
          } else {
            cat("无相关性数据，不使用环境变量\n")
            c()
          }

          # 提取选定的环境变量值
          if(!is.null(selected_vars) && length(selected_vars) > 0) {
            # 合并模式下使用第一个县城作为参考
            ref_county_for_env <- if(config$combine_counties) group_counties[1] else group_name
            layer_data <- extract_environmental_values(layer_data, selected_vars, config, ref_county_for_env, year, layer_name)
          }

          # 获取可用的环境变量
          env_variables <- if(!is.null(selected_vars)) {
            intersect(selected_vars, names(layer_data))
          } else {
            c()
          }
          
          cat("最终使用的环境变量:", length(env_variables), "个:", paste(env_variables, collapse = ", "), "\n")

          # 准备建模数据
          data_prep_result <- prepare_data(layer_data, soil_property, config, "mixed", env_variables, include_prediction_data = FALSE)

          if(is.null(data_prep_result)) {
            cat("数据准备失败\n")
            next
          }

          modeling_data <- data_prep_result$modeling_data
          
          if(is.null(modeling_data) || nrow(modeling_data) < config$general_options$min_samples_for_modeling) {
            cat("建模数据不足，启用分层保底策略\n")
            # 启用分层保底策略
            ref_county <- if(config$combine_counties) group_counties[1] else group_name
            fallback_result <- apply_hierarchical_fallback(
              filled_data, ref_county, year, soil_property, layer_name, missing_count, config
            )

            if(!is.null(fallback_result)) {
              filled_data <- fallback_result$data
              gap_filling_log[[paste(group_name, year, soil_property, layer_name, sep="_")]] <-
                create_filling_log_entry(
                  group_name, year, soil_property, layer_name,
                  missing_count, missing_count, fallback_result$method,
                  NA, NA, sample_size = nrow(layer_data),
                  total_count = nrow(layer_data)
                )
              cat("保底策略成功填补", missing_count, "个缺失值\n")
            } else {
              cat("保底策略也失败\n")
            }
            next
          }

          # 组合环境变量和邻近层变量（不包括坐标）
          modeling_features <- c(env_variables, data_prep_result$neighbor_vars)
          
          cat("实际可用环境变量:", length(env_variables), "个:", paste(env_variables, collapse = ", "), "\n")
          cat("邻近层变量:", length(data_prep_result$neighbor_vars), "个:", paste(data_prep_result$neighbor_vars, collapse = ", "), "\n")
          cat("建模使用的特征变量:", length(modeling_features), "个:", paste(modeling_features, collapse = ", "), "\n")
          
          # 步骤4：三种方法对比
          modeling_results <- compare_three_methods(
            modeling_data, soil_property, layer_name, env_variables, config
          )
          
          if(is.null(modeling_results$best_method)) {
            cat("所有方法都失败，启用分层保底策略\n")
            # 启用分层保底策略
            ref_county <- if(config$combine_counties) group_counties[1] else group_name
            fallback_result <- apply_hierarchical_fallback(
              filled_data, ref_county, year, soil_property, layer_name, missing_count, config
            )

            if(!is.null(fallback_result)) {
              filled_data <- fallback_result$data
              gap_filling_log[[paste(group_name, year, soil_property, layer_name, sep="_")]] <-
                create_filling_log_entry(
                  group_name, year, soil_property, layer_name,
                  missing_count, missing_count, fallback_result$method,
                  NA, NA, sample_size = if(exists("modeling_data") && !is.null(modeling_data)) nrow(modeling_data) else NA,
                  total_count = nrow(layer_data)
                )
              cat("保底策略成功填补", missing_count, "个缺失值\n")
            } else {
              cat("保底策略也失败\n")
            }
            next
          }
          
          cat("最佳方法:", modeling_results$best_method,
              "，R² =", round(modeling_results$best_r2, 3), "\n")

          # 步骤5：使用最佳方法进行预测
          filled_result <- predict_with_best_method(
            modeling_results, filled_data, group_counties, year, soil_property, layer_name, config
          )

          if(!is.null(filled_result)) {
            # 检查预测结果是否还有NA值或负值
            has_invalid_values <- FALSE
            invalid_count <- 0

            # 检查NA值
            if(!is.null(filled_result$values) && !is.null(filled_result$na_count) && filled_result$na_count > 0) {
              has_invalid_values <- TRUE
              invalid_count <- filled_result$na_count
              cat("模型预测存在", filled_result$na_count, "个NA值")
            }

            # 检查负值
            if(!is.null(filled_result$values) && !is.null(filled_result$negative_count) && filled_result$negative_count > 0) {
              has_invalid_values <- TRUE
              invalid_count <- invalid_count + filled_result$negative_count
              if(filled_result$na_count > 0) {
                cat("和", filled_result$negative_count, "个负值")
              } else {
                cat("模型预测存在", filled_result$negative_count, "个负值")
              }
              cat("，这与实际情况不符")
            }

            # 如果存在无效值，启用保底策略
            if(has_invalid_values) {
              cat("，启用保底策略\n")
              # 先更新已有的预测值
              filled_data <- update_filled_data(
                filled_data, filled_result, group_counties, year, soil_property, layer_name, config
              )

              # 对剩余无效值启用保底策略
              ref_county <- if(config$combine_counties) group_counties[1] else group_name
              fallback_result <- apply_hierarchical_fallback(
                filled_data, ref_county, year, soil_property, layer_name, invalid_count, config
              )

              if(!is.null(fallback_result)) {
                filled_data <- fallback_result$data
                cat("保底策略成功填补剩余", invalid_count, "个无效值\n")
              }
            } else {
              # 更新填补后的数据
              if(!is.null(filled_result$updated_data)) {
                # 分层保底策略返回的是更新后的完整数据
                filled_data <- filled_result$updated_data
              } else {
                # 正常预测结果，使用标准更新方法
                filled_data <- update_filled_data(
                  filled_data, filled_result, group_counties, year, soil_property, layer_name, config
                )
              }
            }

            # 记录填补日志
            if(!is.null(filled_result$updated_data)) {
              # 分层保底策略
              gap_filling_log[[paste(group_name, year, soil_property, layer_name, sep="_")]] <-
                create_filling_log_entry(
                  group_name, year, soil_property, layer_name,
                  missing_count, missing_count, filled_result$method,
                  NA, NA, sample_size = nrow(layer_data),
                  total_count = nrow(layer_data)
                )
              cat("分层保底策略成功填补", missing_count, "个缺失值\n")
            } else {
              # 正常预测结果，记录实际使用的方法
              actual_method <- filled_result$method %||% modeling_results$best_method
              actual_result <- if(!is.null(filled_result$method)) {
                # 如果有实际使用的方法信息，从all_results中获取
                modeling_results$all_results[[filled_result$method]]
              } else {
                modeling_results$best_result
              }

              # 计算实际填补的数量（排除NA值）
              actual_filled_count <- if(!is.null(filled_result$values)) {
                sum(!is.na(filled_result$values))
              } else {
                0
              }

              gap_filling_log[[paste(county, year, soil_property, layer_name, sep="_")]] <-
                create_filling_log_entry(
                  county, year, soil_property, layer_name,
                  missing_count, actual_filled_count, actual_method,
                  ifelse(is.null(actual_result), modeling_results$best_r2, actual_result$r_squared),
                  ifelse(is.null(actual_result), modeling_results$best_rmse, actual_result$rmse),
                  ifelse(is.null(actual_result$mae), NA, actual_result$mae),
                  ifelse(is.null(actual_result$ccc), NA, actual_result$ccc),
                  ifelse(is.null(actual_result$rpd), NA, actual_result$rpd),
                  modeling_results$all_results,
                  sample_size = nrow(modeling_data),
                  selected_variables = modeling_features,
                  total_count = nrow(layer_data)
                )
              cat("成功填补", actual_filled_count, "个缺失值\n")
            }
          } else {
            cat("模型预测失败，启用分层保底策略\n")
            # 启用分层保底策略
            ref_county <- if(config$combine_counties) group_counties[1] else group_name
            fallback_result <- apply_hierarchical_fallback(
              filled_data, ref_county, year, soil_property, layer_name, missing_count, config
            )

            if(!is.null(fallback_result)) {
              filled_data <- fallback_result$data
              gap_filling_log[[paste(group_name, year, soil_property, layer_name, sep="_")]] <-
                create_filling_log_entry(
                  group_name, year, soil_property, layer_name,
                  missing_count, missing_count, fallback_result$method,
                  NA, NA, sample_size = nrow(layer_data),
                  total_count = nrow(layer_data)
                )
              cat("保底策略成功填补", missing_count, "个缺失值\n")
            } else {
              cat("保底策略也失败\n")
            }
          }
        }
      }
    }
  }
  
  # 步骤6：最终验证和保存结果
  cat("\n=== 最终验证填补结果 ===\n")

  # 验证所有处理过的县城、年份和属性的填补情况
  for(county in counties) {
    for(year in years) {
      cat("\n", county, "县", year, "年填补情况:\n")
      test_data <- filled_data %>%
        filter(City == county, year == !!year)

      if(nrow(test_data) == 0) {
        cat("  无数据\n")
        next
      }

      for(soil_property in soil_properties) {
        cat("  ", soil_property, ":\n")
        for(depth_config in config$general_options$depth_layers) {
          layer_data <- test_data %>%
            filter(`深度中点` == depth_config$center)

          if(nrow(layer_data) > 0) {
            missing_count <- sum(is.na(layer_data[[soil_property]]))
            total_count <- nrow(layer_data)
            cat("    ", depth_config$name, ": ", missing_count, "/", total_count, " 缺失\n")
          }
        }
      }
    }
  }

  save_gap_filling_results(filled_data, gap_filling_log, config)
  
  cat("\n=== 缺失值填补分析完成 ===\n")
  
  return(list(
    filled_data = filled_data,
    gap_filling_log = gap_filling_log,
    original_data = soil_data
  ))
}

# =============================================================================
# 程序入口（全自动运行）
# =============================================================================
cat("\n=== 缺失值填补系统 - 全自动运行 ===\n")
cat("正在启动缺失值填补分析...\n")

# 直接运行分析
results <- run_gap_filling_analysis()

cat("\n=== 分析完成 ===\n")
if(!is.null(results)) {
  cat("✅ 缺失值填补分析成功完成\n")
  cat("📊 填补任务数:", length(results$gap_filling_log), "\n")
  cat("📁 请查看输出目录中的结果文件\n")
} else {
  cat("❌ 分析过程中出现问题\n")
}

cat("缺失值填补主程序加载完成\n")
